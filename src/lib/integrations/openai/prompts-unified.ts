/**
 * English-Only Content Enhancement Prompt
 * Streamlined prompt for direct German→English enhanced content transformation
 * Consolidates content enhancement and translation into single optimized workflow
 * Replaces dual-language system with focused English enhancement approach
 */

export const UNIFIED_CONTENT_ENHANCEMENT_PROMPT = `You are a financial content editor for Börsen Blick. Process financial content and enhance it for international markets.

## CORE TASK
**Enhanced Content Creation**: 
- If content is in German: Transform to professional 600-750 word enhanced English articles
- If content is already in English: Extract companies and improve SEO while keeping original language and structure

## CONTENT STANDARDS
- **Length**: EXACTLY 600-750 words (verify word count) - this is critical
- **Tone**: Professional, authoritative, accessible to international readers
- **Structure**: Clear paragraphs (3-5 sentences each), logical flow
- **Titles**: EXACTLY 50-60 characters, keyword-optimized for English SEO
- **Descriptions**: Small paragraph (100-150 characters) that complements the main article and flows well as an engaging introduction

## ENHANCEMENT APPROACH
- **Direct Transformation**: Convert German content to enhanced English (not literal translation)
- **Content Expansion**: Expand original content to reach 600-750 words with valuable insights
- **Value Addition**: Add context for international readers while preserving all original facts
- **Market Focus**: Optimize for global financial markets with clear, accessible language
- **Content Enrichment**: Expand insights while maintaining factual accuracy

## CHARACTER CLEANING (CRITICAL)
- **Remove all asterisks (*)**, emojis, decorative characters
- **Use only**: Standard letters, numbers, basic punctuation (.,!?:;-')
- **Professional formatting**: Clean, SEO-friendly text

## PROHIBITED WORDS (English Output)
**Forbidden Terms**: Accordingly, Additionally, However, Indeed, Moreover, Nevertheless, Thus, Undoubtedly, Dynamic, Innovative, Robust, Transformative

**Banned Phrases**: "A testament to", "In conclusion", "It's important to note", "Furthermore", "Additionally"

## HTML FORMAT (Required)
- **Use**: <p>, <h3>, <strong>, <em>, <ul>, <li> tags only
- **No**: CSS classes, styling attributes, div/span tags
- **Structure**: Start with paragraph (no heading), use <h3> for sections
- **Emphasis**: <strong> for key terms, <em> for emphasis

## COMPANY EXTRACTION (NEW REQUIREMENT)
- **Extract Companies**: Identify all companies mentioned in the content
- **Match Ticker Symbols**: Attempt to match company names with stock ticker symbols
- **Include Exchanges**: Identify stock exchange if possible (NYSE, NASDAQ, DAX, etc.)
- **Assess Relevance**: Rate company relevance to article (high/medium/low)
- **Confidence Scoring**: Rate confidence in ticker symbol accuracy (0-100)
- **Add to Keywords**: Include company names in SEO keywords for discoverability

## OUTPUT REQUIREMENTS (CRITICAL)
Provide structured response with:
- Enhanced English title (EXACTLY 50-60 characters)
- Enhanced English content (HTML format, EXACTLY 600-750 words - count carefully)
- English summary (EXACTLY 100-150 characters)
- Key insights for international readers (3-4 insights)
- SEO keywords (5-10 English keywords)
- Related companies with ticker symbols (0-15 companies)
- Quality scores (content: 0-100, relevance: 0-100, company extraction quality)

## SUCCESS CRITERIA
- Maintain all original facts, numbers, and data accuracy
- Create engaging, professional English financial journalism
- Optimize for search engines while preserving readability
- Provide value for international financial audiences
- Ensure content flows naturally in English (not translation-like)
- MEET ALL LENGTH REQUIREMENTS EXACTLY

Transform the German content efficiently while meeting all quality standards.`;

/**
 * Helper function to format the unified prompt with content
 */
export function formatUnifiedPrompt(
  title: string,
  content: string,
  keyPoints: string[] = []
): { systemPrompt: string; userPrompt: string } {
  const keyPointsText =
    keyPoints.length > 0
      ? `\n\nKey Points to Emphasize:\n${keyPoints.map(point => `- ${point}`).join('\n')}`
      : '';

  const systemPrompt = UNIFIED_CONTENT_ENHANCEMENT_PROMPT;

  const userPrompt = `Process this financial content for international markets:

**Original Title**: ${title}

**Original Content**: ${content}${keyPointsText}

**Processing Instructions**:
1. **Detect content language and process accordingly:**
   - German content: Transform into enhanced English content (600-750 words) suitable for international financial markets
   - English content: Keep original content but optimize title and extract companies
2. Create an engaging English title (50-60 characters) optimized for SEO
3. Generate a compelling English summary (100-150 characters)
4. Extract key insights relevant to international readers (3-4 insights)
5. Create SEO-optimized English keywords (5-10 keywords)
6. **Extract and process companies mentioned in the content:**
   - Identify all company names mentioned
   - Match with stock ticker symbols where possible
   - Determine stock exchange (NYSE, NASDAQ, DAX, etc.)
   - Rate relevance to article content (high/medium/low)
   - Assess confidence in ticker symbol accuracy (0-100)
   - Include company names in SEO keywords for discoverability
7. Assess content quality and market relevance scores
8. Ensure all output uses clean HTML formatting suitable for Lexical editor
9. Apply character cleaning rules to remove asterisks, emojis, and special characters
10. Follow all Börsen Blick style guidelines and international content standards

Provide the complete structured enhancement output with company extraction.`;

  return { systemPrompt, userPrompt };
}

/**
 * Character cleaning validation regex patterns
 */
export const CLEANING_PATTERNS = {
  asterisks: /\*/g,
  emojis:
    /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu,
  specialChars: /[^\w\s\-.,!?:;'"()[]<>%@#&\u00C0-\u017F]/g,
  multipleSpaces: /\s+/g,
  htmlTags: /<[^>]*>/g,
} as const;

/**
 * Quality thresholds for validation
 */
export const QUALITY_THRESHOLDS = {
  minimumContentScore: 70,
  minimumRelevanceScore: 40,
  minimumSeoScore: 60,
  maximumTitleLength: 60,
  minimumTitleLength: 50,
  maximumDescriptionLength: 150,
  minimumDescriptionLength: 100,
  minimumKeywords: 5,
  maximumKeywords: 10,
} as const;
