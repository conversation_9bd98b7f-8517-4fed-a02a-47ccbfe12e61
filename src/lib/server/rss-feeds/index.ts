// Removed unused import - function now uses inline parameters
import config from '@payload-config';
import { getPayload } from 'payload';

/**
 * Update RSS feed statistics after processing
 */
export async function updateRSSFeedStats(
  feedId: string,
  stats?: {
    processed?: number;
    accepted?: number;
    articlesFoundSinceLastSuccessful?: number;
    success?: boolean;
    errorMessage?: string;
  }
): Promise<void> {
  try {
    const payload = await getPayload({ config });

    // Get current feed data to update counters
    const currentFeed = await payload.findByID({
      collection: 'rss-feeds',
      id: feedId,
    });

    const updateData: any = {
      lastChecked: new Date().toISOString(),
    };

    if (stats?.success !== undefined) {
      if (stats.success) {
        // Successful processing
        updateData.lastSuccessfulCheck = new Date().toISOString();
        updateData.errorCount = 0;
        updateData.lastErrorMessage = null;

        if (stats.articlesFoundSinceLastSuccessful !== undefined) {
          updateData.articlesFoundSinceLastSuccessful =
            stats.articlesFoundSinceLastSuccessful;
        }
      } else {
        // Failed processing
        updateData.errorCount = (currentFeed.errorCount || 0) + 1;
        if (stats.errorMessage) {
          updateData.lastErrorMessage = stats.errorMessage;
        }
      }
    }

    // Update cumulative counters
    if (stats?.processed !== undefined) {
      updateData.itemsProcessed =
        (currentFeed.itemsProcessed || 0) + stats.processed;
    }

    if (stats?.accepted !== undefined) {
      updateData.itemsAccepted =
        (currentFeed.itemsAccepted || 0) + stats.accepted;
    }

    await payload.update({
      collection: 'rss-feeds',
      id: feedId,
      data: updateData,
    });

    console.log(`✅ Updated RSS feed stats: ${feedId}`, updateData);
  } catch (error: any) {
    console.error('❌ Failed to update RSS feed stats:', error);
    throw new Error(`Failed to update RSS feed stats: ${error.message}`);
  }
}

/**
 * Get all active RSS feeds
 */
export async function getActiveRSSFeeds(): Promise<any[]> {
  try {
    const payload = await getPayload({ config });

    const result = await payload.find({
      collection: 'rss-feeds',
      where: {
        isActive: {
          equals: true,
        },
      },
      limit: 100,
      sort: 'name',
    });

    return result.docs;
  } catch (error: any) {
    console.error('❌ Failed to get active RSS feeds:', error);
    throw new Error(`Failed to get active RSS feeds: ${error.message}`);
  }
}

/**
 * Get RSS feed by ID
 */
export async function getRSSFeedById(id: string): Promise<any> {
  try {
    const payload = await getPayload({ config });

    const result = await payload.findByID({
      collection: 'rss-feeds',
      id,
    });

    return result;
  } catch (error: any) {
    console.error('❌ Failed to get RSS feed by ID:', error);
    throw new Error(`Failed to get RSS feed by ID: ${error.message}`);
  }
}

/**
 * Create a new RSS feed
 */
export async function createRSSFeed(data: {
  name: string;
  url: string;
  category: string;
  language: string;
  isActive?: boolean;
}): Promise<{ id: string }> {
  try {
    const payload = await getPayload({ config });

    const result = await payload.create({
      collection: 'rss-feeds',
      data: {
        name: data.name,
        url: data.url,
        isActive: data.isActive ?? true,
      },
    });

    console.log(`✅ Created RSS feed: ${data.name} (ID: ${result.id})`);
    return { id: result.id.toString() };
  } catch (error: any) {
    console.error('❌ Failed to create RSS feed:', error);
    throw new Error(`Failed to create RSS feed: ${error.message}`);
  }
}

/**
 * Update RSS feed
 */
export async function updateRSSFeed(
  id: string,
  data: {
    name?: string;
    url?: string;
    category?: 'general' | 'markets' | 'companies' | 'economy' | 'technology';
    language?: 'de' | 'en';
    isActive?: boolean;
  }
): Promise<void> {
  try {
    const payload = await getPayload({ config });

    await payload.update({
      collection: 'rss-feeds',
      id,
      data,
    });

    console.log(`✅ Updated RSS feed: ${id}`);
  } catch (error: any) {
    console.error('❌ Failed to update RSS feed:', error);
    throw new Error(`Failed to update RSS feed: ${error.message}`);
  }
}

/**
 * Delete RSS feed
 */
export async function deleteRSSFeed(id: string): Promise<void> {
  try {
    const payload = await getPayload({ config });

    await payload.delete({
      collection: 'rss-feeds',
      id,
    });

    console.log(`✅ Deleted RSS feed: ${id}`);
  } catch (error: any) {
    console.error('❌ Failed to delete RSS feed:', error);
    throw new Error(`Failed to delete RSS feed: ${error.message}`);
  }
}

/**
 * Get RSS feed processing statistics
 */
export async function getRSSFeedStats(): Promise<{
  totalFeeds: number;
  activeFeeds: number;
  totalProcessed: number;
  totalAccepted: number;
  overallAcceptanceRate: number;
}> {
  try {
    const payload = await getPayload({ config });

    const allFeeds = await payload.find({
      collection: 'rss-feeds',
      limit: 1000,
    });

    const activeFeeds = allFeeds.docs.filter((feed: any) => feed.isActive);

    const totalProcessed = allFeeds.docs.reduce(
      (sum: number, feed: any) => sum + (feed.itemsProcessed || 0),
      0
    );

    const totalAccepted = allFeeds.docs.reduce(
      (sum: number, feed: any) => sum + (feed.itemsAccepted || 0),
      0
    );

    const overallAcceptanceRate =
      totalProcessed > 0 ? (totalAccepted / totalProcessed) * 100 : 0;

    return {
      totalFeeds: allFeeds.docs.length,
      activeFeeds: activeFeeds.length,
      totalProcessed,
      totalAccepted,
      overallAcceptanceRate,
    };
  } catch (error: any) {
    console.error('❌ Failed to get RSS feed stats:', error);
    throw new Error(`Failed to get RSS feed stats: ${error.message}`);
  }
}
