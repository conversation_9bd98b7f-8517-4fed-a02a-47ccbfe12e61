'use client';

import { useState } from 'react';
import { useDocumentInfo, useAllFormFields } from '@payloadcms/ui';
import { useToast } from '@/hooks/use-toast';
import { ToastProvider } from '@/components/admin/toast-provider';
import { reduceFieldsToValues } from 'payload/shared';

export const ArticleDocumentControls = () => {
  const { id } = useDocumentInfo();
  const [fields, dispatchFields] = useAllFormFields();
  const { toast } = useToast();
  const [isTranslating, setIsTranslating] = useState(false);

  // Convert fields to data using PayloadCMS helper
  const data = reduceFieldsToValues(fields, true);
  const workflowStage = data?.workflowStage;
  const articleType = data?.articleType;
  const hasEnhancedEnglish =
    data?.englishTab?.enhancedTitle && data?.englishTab?.enhancedContent;
  const hasGermanTranslation =
    data?.germanTab?.germanTitle && data?.germanTab?.germanContent;

  // Only show for generated articles with appropriate workflow stage
  if (
    articleType !== 'generated' ||
    ![
      'candidate-article',
      'translated',
      'ready-for-review',
      'published',
    ].includes(workflowStage)
  ) {
    return null;
  }

  // Translation button should be available when:
  // 1. Article has enhanced English content
  // 2. Article is in appropriate workflow stage for translation (candidate-article, translated, ready-for-review, published)
  // 3. Show for both new translation and re-translation scenarios
  const canTranslate =
    hasEnhancedEnglish &&
    [
      'candidate-article',
      'translated',
      'ready-for-review',
      'published',
    ].includes(workflowStage);
  const showTranslationButton = canTranslate;

  const handleTranslateToGerman = async () => {
    if (!id) {
      toast.error('No article ID found');
      return;
    }

    if (!hasEnhancedEnglish) {
      toast.error(
        'Article must have enhanced English content before translation'
      );
      return;
    }

    setIsTranslating(true);

    try {
      const response = await fetch('/api/articles/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ articleId: id }),
      });

      const result = await response.json();

      if (result.success && result.translatedContent) {
        console.log('✅ Translation successful, updating form state...');

        // For re-translation, we need to ensure the form properly reflects the updated content
        // The API has already updated the database, so we need to sync the form state

        // Update the hasGermanTranslation flag first
        dispatchFields({
          type: 'UPDATE',
          path: 'hasGermanTranslation',
          value: true,
        });

        // Update workflow stage to "translated" if it's currently "candidate-article"
        if (workflowStage === 'candidate-article') {
          dispatchFields({
            type: 'UPDATE',
            path: 'workflowStage',
            value: 'translated',
          });
        }

        // Update German tab fields with the new translated content
        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanTitle',
          value: result.translatedContent.germanTitle,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanContent',
          value: result.translatedContent.germanContent,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanSummary',
          value: result.translatedContent.germanSummary,
        });

        if (result.translatedContent.germanKeywords) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanKeywords',
            value: result.translatedContent.germanKeywords.map(
              (keyword: string) => ({
                keyword,
              })
            ),
          });
        }

        if (result.translatedContent.germanKeyInsights) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanKeyInsights',
            value: result.translatedContent.germanKeyInsights.map(
              (insight: string) => ({
                insight,
              })
            ),
          });
        }

        console.log(
          '📄 Form state updated, reloading page to reflect changes...'
        );

        // Always reload the page to ensure the updated German content is visible
        // This is especially important for re-translation where the form needs to sync with the database
        setTimeout(() => {
          window.location.reload();
        }, 500); // Shorter delay for better UX

        // No toast notification - let PayloadCMS's native form state be the feedback
      } else {
        toast.error('Translation failed', {
          description:
            result.error || 'An unknown error occurred during translation',
        });
      }
    } catch (error) {
      console.error('Translation error:', error);
      toast.error('Translation failed', {
        description: 'Failed to communicate with the translation service',
      });
    } finally {
      setIsTranslating(false);
    }
  };

  // Determine button states based on workflow stage and content availability
  const isCandidate = workflowStage === 'candidate-article';
  const isTranslated = workflowStage === 'translated';
  const isReadyForReview = workflowStage === 'ready-for-review';
  const isPublished = workflowStage === 'published';

  const getTranslationButtonText = () => {
    if (isTranslating) return 'Translating to German...';
    if (hasGermanTranslation) return 'Re-Translate to German';
    return 'Translate to German';
  };

  const getTranslationButtonColor = () => {
    if (hasGermanTranslation) return '#f59e0b'; // Orange for re-translate
    return '#2563eb'; // Blue for initial translate
  };

  const isTranslationDisabled = isTranslating || !showTranslationButton;

  return (
    <ToastProvider>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
        {/* Translation Button - Only show when conditions are met */}
        {showTranslationButton && (
          <button
            onClick={handleTranslateToGerman}
            disabled={isTranslationDisabled}
            style={{
              backgroundColor: getTranslationButtonColor(),
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '0.375rem',
              cursor: isTranslationDisabled ? 'not-allowed' : 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500',
              opacity: isTranslationDisabled ? 0.6 : 1,
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
            }}
          >
            {isTranslating && (
              <div
                style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid rgba(255, 255, 255, 0.3)',
                  borderTop: '2px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite',
                }}
              />
            )}
            {getTranslationButtonText()}
          </button>
        )}

        {/* Status Information for Articles without Enhanced Content */}
        {!hasEnhancedEnglish &&
          (isCandidate || isTranslated || isReadyForReview) && (
            <div
              style={{
                padding: '0.5rem 1rem',
                borderRadius: '0.375rem',
                fontSize: '0.875rem',
                backgroundColor: '#fef3c7',
                color: '#92400e',
                border: '1px solid #fcd34d',
              }}
            >
              Awaiting English Enhancement
            </div>
          )}

        {/* German Translation Status - removed to reduce UI clutter */}
      </div>
    </ToastProvider>
  );
};
