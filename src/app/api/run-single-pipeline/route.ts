import config from '@payload-config';
import { NextResponse } from 'next/server';
import { getPayload } from 'payload';
import { getFirecrawlStats } from '@/lib/integrations/firecrawl/enhanced-client';
import { parseRSSFeed } from '@/lib/integrations/rss/parser';
import { createCandidateArticle } from '@/lib/server/create-candidate';
import {
  checkUrlExists,
  createProcessedUrl,
} from '@/lib/server/processed-urls/index';
import { extractContentEnhanced } from '@/lib/integrations/firecrawl/enhanced-client';

/**
 * Single Article Pipeline API
 *
 * Executes a limited RSS-to-articles workflow for testing production logic:
 * 1. Fetch all active RSS feeds
 * 2. Parse RSS content and try up to 10 articles from each feed
 * 3. Match keywords against title/description
 * 4. Extract full content via Firecrawl for matching articles
 * 5. Create candidate articles for accepted content
 *
 * This endpoint provides a middle ground between the test pipeline (static URLs)
 * and the full production pipeline (all articles). Perfect for testing the
 * production workflow with real RSS feeds but limited scope.
 *
 * Key Features:
 * - Uses real RSS feeds from database
 * - Applies full keyword filtering
 * - Tries up to 10 articles per feed to find keyword matches
 * - Processes exactly 1 successful article per feed
 * - Maintains all production safety checks
 * - Ideal for testing feed configurations
 * - Ensures successful processing from each active feed
 */

interface ProcessingDetail {
  url: string;
  title: string;
  feedName: string;
  status: 'accepted' | 'rejected' | 'error' | 'rate_limited';
  reason: string;
  articleId?: string;
}

interface ProcessingResult {
  success: boolean;
  processed: number;
  accepted: number;
  rejected: number;
  errors: string[];
  details: ProcessingDetail[];
}

export async function POST() {
  const startTime = Date.now();

  try {
    console.log('🎯 Starting single article pipeline...');
    console.log(`⏰ Start time: ${new Date(startTime).toISOString()}`);

    const payload = await getPayload({ config });

    // Step 1: Validate database state
    console.log('🔍 Validating database state...');

    const [rssFeeds, keywords, categories] = await Promise.all([
      payload.find({
        collection: 'rss-feeds',
        where: { isActive: { equals: true } },
        limit: 100,
      }),
      payload.find({
        collection: 'keywords' as any,
        where: { isActive: { equals: true } },
        limit: 100,
      }),
      payload.find({ collection: 'categories', limit: 100 }),
    ]);

    if (rssFeeds.totalDocs === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'No active RSS feeds found',
          message:
            'Please create RSS feeds first using: node scripts/content-load.mjs',
        },
        { status: 400 }
      );
    }

    if (keywords.totalDocs === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'No keywords found',
          message:
            'Please create keywords first using: node scripts/content-load.mjs',
        },
        { status: 400 }
      );
    }

    console.log(`✅ Database validation passed:`);
    console.log(`   - Active RSS feeds: ${rssFeeds.totalDocs}`);
    rssFeeds.docs.forEach((feed: any, index: number) => {
      console.log(
        `     ${index + 1}. ${feed.name} (${feed.priority}) - ${feed.language}`
      );
    });
    console.log(`   - Active keywords: ${keywords.totalDocs}`);
    const keywordList = keywords.docs
      .map((k: any) => k.keyword)
      .slice(0, 10)
      .join(', ');
    console.log(
      `     Keywords: ${keywordList}${keywords.totalDocs > 10 ? '...' : ''}`
    );
    console.log(`   - Categories: ${categories.totalDocs}`);

    // Step 2: Process single article from each RSS feed
    console.log('\n📡 Processing single article from each RSS feed...');
    console.log(
      '🔄 Processing feeds in priority order (high → medium → low)...'
    );

    const result: ProcessingResult = {
      success: true,
      processed: 0,
      accepted: 0,
      rejected: 0,
      errors: [],
      details: [],
    };

    // Sort feeds by priority (high → medium → low)
    const sortedFeeds = rssFeeds.docs.sort((a: any, b: any) => {
      const priorityOrder: { [key: string]: number } = {
        high: 0,
        medium: 1,
        low: 2,
      };
      return (
        (priorityOrder[a.priority] || 3) - (priorityOrder[b.priority] || 3)
      );
    });

    for (let i = 0; i < sortedFeeds.length; i++) {
      const feed = sortedFeeds[i];
      console.log(
        `\n📰 [${i + 1}/${sortedFeeds.length}] Processing feed: ${feed.name}`
      );

      try {
        const feedResult = await processSingleFeedArticle(feed, keywords.docs);
        if (feedResult) {
          result.details.push(feedResult);
          result.processed++;

          if (feedResult.status === 'accepted') {
            result.accepted++;
            console.log(`   ✅ Accepted: ${feedResult.title}`);
          } else if (feedResult.status === 'rejected') {
            result.rejected++;
            console.log(`   ❌ Rejected: ${feedResult.reason}`);
          } else if (feedResult.status === 'error') {
            result.errors.push(`${feed.name}: ${feedResult.reason}`);
            console.log(`   💥 Error: ${feedResult.reason}`);
          } else if (feedResult.status === 'rate_limited') {
            console.log(`   ⏳ Rate limited: ${feedResult.reason}`);
          }
        } else {
          console.log(`   📭 No articles found in feed`);
        }
      } catch (error) {
        const errorMsg = `Failed to process feed ${feed.name}: ${error instanceof Error ? error.message : String(error)}`;
        console.error('❌', errorMsg);
        result.errors.push(errorMsg);
        result.details.push({
          url: 'unknown',
          title: 'unknown',
          feedName: feed.name,
          status: 'error',
          reason: error instanceof Error ? error.message : String(error),
        });
      }
    }

    // Step 3: Calculate processing time
    const processingTime = Date.now() - startTime;
    const processingTimeSeconds = Math.round(processingTime / 1000);

    // Step 4: Get updated article counts
    const articleStats = await Promise.all([
      payload.find({
        collection: 'articles',
        where: { workflowStage: { equals: 'candidate-article' } },
        limit: 1000,
      }),
      payload.find({
        collection: 'articles',
        where: { workflowStage: { equals: 'published' } },
        limit: 1000,
      }),
      payload.find({ collection: 'articles', limit: 1000 }),
    ]);

    // Step 5: Get Firecrawl API usage stats
    const firecrawlStats = getFirecrawlStats();

    // Step 6: Prepare comprehensive response
    const response = {
      success: result.success,
      message: 'Single article pipeline execution completed',
      timing: {
        processingTimeMs: processingTime,
        processingTimeSeconds: processingTimeSeconds,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date().toISOString(),
      },
      pipeline: {
        feedsProcessed: sortedFeeds.length,
        articlesProcessed: result.processed,
        accepted: result.accepted,
        rejected: result.rejected,
        errorCount: result.errors.length,
        successRate:
          result.processed > 0
            ? Math.round((result.accepted / result.processed) * 100)
            : 0,
      },
      firecrawl: {
        totalRequests: firecrawlStats.totalRequests,
        successfulRequests: firecrawlStats.successfulRequests,
        failedRequests: firecrawlStats.failedRequests,
        successRate:
          firecrawlStats.totalRequests > 0
            ? Math.round(
                (firecrawlStats.successfulRequests /
                  firecrawlStats.totalRequests) *
                  100
              )
            : 0,
        errors: {
          rateLimits: firecrawlStats.rateLimitErrors,
          configuration: firecrawlStats.configErrors,
          authentication: firecrawlStats.authErrors,
          timeouts: firecrawlStats.timeoutErrors,
        },
      },
      database: {
        activeFeeds: rssFeeds.totalDocs,
        activeKeywords: keywords.totalDocs,
        totalCategories: categories.totalDocs,
        candidateArticles: articleStats[0].totalDocs,
        publishedArticles: articleStats[1].totalDocs,
        totalArticles: articleStats[2].totalDocs,
      },
      feeds: sortedFeeds.map((feed: any) => ({
        id: feed.id,
        name: feed.name,
        url: feed.url,
        language: feed.language,
        priority: feed.priority,
      })),
      details: result.details,
      errors: result.errors,
      adminUrls: {
        articles: 'http://localhost:3001/admin/collections/articles',
        candidateArticles:
          'http://localhost:3001/admin/collections/articles?where%5Bstatus%5D%5Bequals%5D=candidate-article',
        rssFeeds: 'http://localhost:3001/admin/collections/rss-feeds',
        keywords: 'http://localhost:3001/admin/collections/keywords',
      },
    };

    // Step 7: Log summary
    console.log('\n📊 Single Article Pipeline Summary:');
    console.log(`   ⏱️  Processing time: ${processingTimeSeconds}s`);
    console.log(`   📡 Feeds processed: ${sortedFeeds.length}`);
    console.log(`   📄 Articles processed: ${result.processed}`);
    console.log(`   ✅ Articles accepted: ${result.accepted}`);
    console.log(`   ❌ Articles rejected: ${result.rejected}`);
    console.log(`   🎯 Success rate: ${response.pipeline.successRate}%`);
    console.log(`   📚 Total articles in DB: ${articleStats[2].totalDocs}`);

    // Log Firecrawl summary
    if (firecrawlStats.totalRequests > 0) {
      console.log('\n🔥 Firecrawl API Summary:');
      console.log(`   📡 Total requests: ${firecrawlStats.totalRequests}`);
      console.log(
        `   ✅ Successful: ${firecrawlStats.successfulRequests} (${response.firecrawl.successRate}%)`
      );
      console.log(`   ❌ Failed: ${firecrawlStats.failedRequests}`);
      if (firecrawlStats.rateLimitErrors > 0) {
        console.log(`   ⏳ Rate limits: ${firecrawlStats.rateLimitErrors}`);
      }
    }

    console.log('\n🎯 Single article pipeline completed successfully!');
    console.log(
      `📋 Admin panel: http://localhost:3001/admin/collections/articles`
    );

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Single article pipeline failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error.message,
        message: 'Single article pipeline execution failed',
        timing: {
          processingTimeMs: Date.now() - startTime,
          startTime: new Date(startTime).toISOString(),
          endTime: new Date().toISOString(),
        },
      },
      { status: 500 }
    );
  }
}

/**
 * Process articles from a single RSS feed until finding one with keyword matches
 * Tries up to 10 articles per feed to ensure successful processing
 */
async function processSingleFeedArticle(
  feed: any,
  keywords: any[]
): Promise<ProcessingDetail | null> {
  try {
    console.log(`   🔍 Fetching RSS content from: ${feed.url}`);

    // Parse RSS feed
    const rssItems = await parseRSSFeed(feed.url);

    if (!rssItems || rssItems.length === 0) {
      console.log(`   📭 No articles found in RSS feed`);
      return null;
    }

    console.log(`   📄 Found ${rssItems.length} articles in feed`);

    // Try up to 10 articles to find one with keyword matches
    const maxAttempts = Math.min(10, rssItems.length);
    let rejectedUrls: string[] = [];

    for (let i = 0; i < maxAttempts; i++) {
      const item = rssItems[i];
      const url = item.link;
      const title = item.title || 'Untitled';

      if (!url) {
        console.log(`   ⚠️  Article ${i + 1}: No URL provided, skipping...`);
        continue;
      }

      console.log(
        `   📰 Trying article ${i + 1}/${maxAttempts}: ${title.substring(0, 60)}...`
      );

      // Step 1: Check if URL already processed
      const urlExists = await checkUrlExists(url);
      if (urlExists) {
        console.log(
          `   ⏭️  Article ${i + 1}: Already processed, trying next...`
        );
        continue;
      }

      // Step 2: Basic keyword filtering using predefined keywords
      const hasRelevantKeywords = await checkBasicKeywords(
        title,
        item.description || '',
        keywords
      );

      if (!hasRelevantKeywords) {
        console.log(
          `   ❌ Article ${i + 1}: No keyword matches, trying next...`
        );

        // Mark as processed but rejected
        await createProcessedUrl({
          url,
          title,
          feedId: feed.id.toString(),
          status: 'rejected',
          reason: 'No relevant keywords found',
          publicationDate: item.pubDate ? new Date(item.pubDate) : undefined,
        });

        rejectedUrls.push(url);
        continue; // Try next article
      }

      // Found an article with keyword matches! Process it.
      console.log(`   ✅ Article ${i + 1}: Keywords matched! Processing...`);

      // Step 3: Extract full content using Enhanced Firecrawl
      console.log(`      🔍 Extracting content with enhanced client...`);
      const feedOptions = buildFeedSpecificOptions(feed);
      const enhancedResult = await extractContentEnhanced(url, feedOptions);

      // Convert enhanced result to legacy format for compatibility
      const contentResult = {
        success: enhancedResult.success,
        content:
          enhancedResult.formats.html || enhancedResult.formats.markdown || '',
        title: enhancedResult.metadata?.title,
        author: enhancedResult.metadata?.author,
        publishedDate: enhancedResult.metadata?.publishDate
          ? new Date(enhancedResult.metadata.publishDate)
          : undefined,
        error:
          enhancedResult.errors.length > 0
            ? enhancedResult.errors.join(', ')
            : undefined,
        metadata: {
          wordCount: enhancedResult.metadata?.wordCount || 0,
          readingTime: enhancedResult.metadata?.readingTime || 0,
        },
      };

      if (!contentResult.success || !contentResult.content) {
        const isRateLimited =
          contentResult.error?.includes('Rate limited') ||
          contentResult.error?.includes('429');

        // For rate limiting, we want to mark as pending retry rather than error
        const status = isRateLimited ? 'pending' : 'error';
        const reason = isRateLimited
          ? 'Rate limited - will retry later'
          : contentResult.error || 'Content extraction failed';

        await createProcessedUrl({
          url,
          title,
          feedId: feed.id.toString(),
          status,
          reason,
          publicationDate: item.pubDate ? new Date(item.pubDate) : undefined,
        });

        if (isRateLimited) {
          console.log(`      ⏳ Rate limited for ${url} - marked for retry`);
          return {
            url,
            title,
            feedName: feed.name,
            status: 'rate_limited',
            reason,
          };
        }

        // For other errors, try next article
        console.log(
          `   ❌ Article ${i + 1}: Content extraction failed, trying next...`
        );
        continue;
      }

      // Step 4: Create candidate article (keywords already matched)
      console.log(`      📝 Creating candidate article`);

      const articleResult = await createCandidateArticle({
        title,
        content: contentResult.content,
        sourceUrl: url,
        sourceFeed: feed.id.toString(),
        publishedDate: item.pubDate ? new Date(item.pubDate) : new Date(),
        author: item.author || undefined,
      });

      // Mark URL as processed and accepted
      await createProcessedUrl({
        url,
        title,
        feedId: feed.id.toString(),
        status: 'accepted',
        reason: 'Keywords matched',
        articleId: articleResult.id,
        publicationDate: item.pubDate ? new Date(item.pubDate) : undefined,
      });

      return {
        url,
        title,
        feedName: feed.name,
        status: 'accepted',
        reason: 'Keywords matched',
        articleId: articleResult.id,
      };
    }

    // If we get here, no articles in the feed had keyword matches
    console.log(
      `   📭 No articles with keyword matches found in ${maxAttempts} attempts`
    );
    return {
      url: rejectedUrls[0] || 'unknown',
      title: rssItems[0]?.title || 'No matching articles',
      feedName: feed.name,
      status: 'rejected',
      reason: `No keyword matches found in ${maxAttempts} articles`,
    };
  } catch (error: any) {
    console.error(`❌ Failed to process feed ${feed.name}:`, error);
    return {
      url: 'unknown',
      title: 'unknown',
      feedName: feed.name,
      status: 'error',
      reason: error.message,
    };
  }
}

/**
 * Basic keyword filtering using predefined keywords from database
 */
async function checkBasicKeywords(
  title: string,
  description: string,
  keywords: any[]
): Promise<boolean> {
  try {
    const text = `${title} ${description}`.toLowerCase();

    // Check if any predefined keyword (German or English) appears in the text
    return keywords.some((dbKeyword: any) => {
      const germanKeyword = dbKeyword.keyword.toLowerCase();
      const englishKeyword = dbKeyword.englishKeyword.toLowerCase();
      return text.includes(germanKeyword) || text.includes(englishKeyword);
    });
  } catch (error: any) {
    console.error('❌ Failed to check keywords:', error.message);
    // Fallback to basic check for common financial terms
    const text = `${title} ${description}`.toLowerCase();
    const fallbackKeywords = [
      'aktien',
      'börse',
      'investment',
      'wirtschaft',
      'finanzen',
      'stock',
      'market',
      'finance',
    ];
    return fallbackKeywords.some(keyword => text.includes(keyword));
  }
}

/**
 * Build feed-specific options for Firecrawl
 */
function buildFeedSpecificOptions(feed: any) {
  const baseOptions = {
    formats: ['html', 'markdown'],
    timeout: 30000,
    waitFor: 2000,
  };

  // Add feed-specific optimizations
  if (feed.url.includes('handelsblatt.com')) {
    return {
      ...baseOptions,
      waitFor: 3000,
      excludeTags: ['nav', 'footer', '.advertisement'],
    };
  }

  if (feed.url.includes('finanzen.net')) {
    return {
      ...baseOptions,
      waitFor: 2500,
      excludeTags: ['.ad-container', '.sidebar'],
    };
  }

  return baseOptions;
}

export async function GET() {
  return NextResponse.json({
    message: 'Single Article Pipeline API',
    description:
      'Tries up to 10 articles from each active RSS feed to find one with keyword matches for testing production workflow',
    usage: {
      method: 'POST',
      endpoint: '/api/run-single-pipeline',
      purpose:
        'Test production workflow with real RSS feeds, ensuring successful processing from each feed',
    },
    features: [
      'Tries up to 10 articles per RSS feed to find keyword matches',
      'Processes exactly one successful article per feed',
      'Full keyword filtering and validation',
      'Production-level error handling',
      'URL deduplication and tracking',
      'Firecrawl content extraction',
      'English-only enhancement system',
      '10MB content size protection',
    ],
    comparison: {
      'test-content-pipeline': 'Static URLs for field/mapping testing',
      'run-single-pipeline':
        'First article from each feed for workflow testing',
      'run-content-pipeline': 'All articles from all feeds for production',
    },
  });
}
