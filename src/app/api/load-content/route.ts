import config from '@payload-config';
import { NextResponse } from 'next/server';
import { getPayload } from 'payload';

/**
 * Content Loading API Endpoint
 *
 * Populates essential collections: RSS feeds, Keywords, Categories
 * Safe to run multiple times (idempotent)
 *
 * GET: Returns current database state
 * POST: Loads sample content into database
 */

export async function POST() {
  const startTime = Date.now();

  try {
    console.log('🚀 Loading essential content into database...');

    const payload = await getPayload({ config });

    let keywordsCreated = 0;
    let categoriesCreated = 0;
    let feedsCreated = 0;

    // Step 1: Create Keywords Collection
    console.log('📝 Creating Keywords...');

    const keywords = [
      { keyword: 'Krypto', englishKeyword: 'Crypto', isActive: true },
      { keyword: 'Gold', englishKeyword: 'Gold', isActive: true },
      { keyword: 'Mineralien', englishKeyword: 'Minerals', isActive: true },
      { keyword: 'Technologie', englishKeyword: 'Technology', isActive: true },
      { keyword: 'Energie', englishKeyword: 'Energy', isActive: true },
      { keyword: 'DAX', englishKeyword: 'DAX', isActive: true },
      { keyword: 'Rohstoffe', englishKeyword: 'Commodities', isActive: true },
      { keyword: 'Wirtschaft', englishKeyword: 'Economy', isActive: true },
      { keyword: 'Aktienmarkt', englishKeyword: 'StockMarket', isActive: true },
      { keyword: 'Währungen', englishKeyword: 'Currencies', isActive: true },
      { keyword: 'Aktien', englishKeyword: 'Stocks', isActive: true },
      { keyword: 'Börsengang', englishKeyword: 'IPO', isActive: true },
      { keyword: 'Fusion', englishKeyword: 'Merger', isActive: true },
      { keyword: 'Nebenwerte', englishKeyword: 'Small Cap', isActive: true },
      {
        keyword: 'Wachstumsaktien',
        englishKeyword: 'Growth Stocks',
        isActive: true,
      },
      { keyword: 'Dividende', englishKeyword: 'Dividend', isActive: true },
      { keyword: 'Wall Street', englishKeyword: 'Wall Street', isActive: true },
      { keyword: 'Prognose', englishKeyword: 'Forecast', isActive: true },
      { keyword: 'Investition', englishKeyword: 'Investment', isActive: true },
      { keyword: 'ETF', englishKeyword: 'ETF', isActive: true },
    ];

    for (const keywordData of keywords) {
      try {
        const existing = await payload.find({
          collection: 'keywords',
          where: { keyword: { equals: keywordData.keyword } },
          limit: 1,
        });

        if (existing.docs.length === 0) {
          await payload.create({
            collection: 'keywords',
            data: keywordData,
          });
          keywordsCreated++;
          console.log(
            `✅ Created keyword: ${keywordData.keyword} (${keywordData.englishKeyword})`
          );
        } else {
          console.log(`⚠️ Keyword exists: ${keywordData.keyword}`);
        }
      } catch (error: any) {
        console.error(
          `❌ Failed to create keyword ${keywordData.keyword}:`,
          error.message
        );
      }
    }

    // Step 2: Create Categories Collection
    console.log('📂 Creating Categories...');

    const categories = [
      { title: 'Investment' },
      { title: 'Economics' },
      { title: 'Technology' },
      { title: 'International' },
    ];

    for (const categoryData of categories) {
      try {
        const existing = await payload.find({
          collection: 'categories',
          where: { title: { equals: categoryData.title } },
          limit: 1,
        });

        if (existing.docs.length === 0) {
          await payload.create({
            collection: 'categories',
            data: categoryData,
          });
          categoriesCreated++;
          console.log(`✅ Created category: ${categoryData.title}`);
        } else {
          console.log(`⚠️ Category exists: ${categoryData.title}`);
        }
      } catch (error: any) {
        console.error(
          `❌ Failed to create category ${categoryData.title}:`,
          error.message
        );
      }
    }

    // Step 3: Create RSS Feeds Collection
    console.log('📡 Creating RSS Feeds...');

    const rssFeeds = [
      {
        name: 'Finanzen.net - News',
        url: 'https://www.finanzen.net/rss/news',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'high' as const,
      },
      {
        name: 'Der Aktionär - News',
        url: 'https://www.deraktionaer.de/aktionaer-news.rss',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'high' as const,
      },
      {
        name: 'Onvista.de - News',
        url: 'https://www.onvista.de/news/feed/rss.xml',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'high' as const,
      },
      {
        name: 'Finanznachrichten.de - Aktien',
        url: 'https://www.finanznachrichten.de/rss-aktien-nachrichten/',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'medium' as const,
      },
      {
        name: 'Wallstreet Online - Nachrichten',
        url: 'https://www.wallstreet-online.de/rss/nachrichten-alle.xml',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'medium' as const,
      },
      {
        name: 'Deutsche Welle - Business (English)',
        url: 'https://rss.dw.com/rdf/rss-en-all',
        isActive: true,
        language: 'en' as const,
        processingFrequency: 720, // 12 hours
        priority: 'low' as const,
      },
      {
        name: 'Handelsblatt - Schlagzeilen',
        url: 'https://www.handelsblatt.com/contentexport/feed/schlagzeilen',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'high' as const,
      },
      {
        name: 'Stern.de - Alle Nachrichten',
        url: 'https://www.stern.de/feed/standard/alle-nachrichten/',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 720, // 12 hours
        priority: 'low' as const,
      },
    ];

    for (const feedData of rssFeeds) {
      try {
        const existing = await payload.find({
          collection: 'rss-feeds',
          where: { url: { equals: feedData.url } },
          limit: 1,
        });

        if (existing.docs.length === 0) {
          await payload.create({
            collection: 'rss-feeds',
            data: feedData,
          });
          feedsCreated++;
          console.log(`✅ Created RSS feed: ${feedData.name}`);
        } else {
          console.log(`⚠️ RSS feed exists: ${feedData.name}`);
        }
      } catch (error: any) {
        console.error(
          `❌ Failed to create RSS feed ${feedData.name}:`,
          error.message
        );
      }
    }

    // Step 4: Get final database stats
    const [keywordStats, categoryStats, feedStats, articleStats] =
      await Promise.all([
        payload.find({ collection: 'keywords', limit: 1000 }),
        payload.find({ collection: 'categories', limit: 1000 }),
        payload.find({ collection: 'rss-feeds', limit: 1000 }),
        payload.find({ collection: 'articles', limit: 1000 }),
      ]);

    const processingTime = Date.now() - startTime;

    const response = {
      success: true,
      message: 'Content loading completed successfully',
      timing: {
        processingTimeMs: processingTime,
        processingTimeSeconds: Math.round(processingTime / 1000),
        startTime: new Date(startTime).toISOString(),
        endTime: new Date().toISOString(),
      },
      created: {
        keywords: keywordsCreated,
        categories: categoriesCreated,
        rssFeeds: feedsCreated,
      },
      totals: {
        keywords: keywordStats.totalDocs,
        categories: categoryStats.totalDocs,
        rssFeeds: feedStats.totalDocs,
        articles: articleStats.totalDocs,
      },
      nextSteps: [
        'Run RSS processing: POST /api/run-content-pipeline',
        'Check admin interface: http://localhost:3001/admin',
        'Monitor article creation in Articles collection',
      ],
      adminUrls: {
        articles: 'http://localhost:3001/admin/collections/articles',
        rssFeeds: 'http://localhost:3001/admin/collections/rss-feeds',
        keywords: 'http://localhost:3001/admin/collections/keywords',
        categories: 'http://localhost:3001/admin/collections/categories',
      },
    };

    console.log('\n📊 Content Loading Summary:');
    console.log(`Keywords created: ${keywordsCreated}/${keywords.length}`);
    console.log(
      `Categories created: ${categoriesCreated}/${categories.length}`
    );
    console.log(`RSS Feeds created: ${feedsCreated}/${rssFeeds.length}`);
    console.log(`Processing time: ${Math.round(processingTime / 1000)}s`);

    return NextResponse.json(response);
  } catch (error: any) {
    const processingTime = Date.now() - startTime;
    console.error('❌ Content loading failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error.message,
        message: 'Content loading failed',
        timing: {
          processingTimeMs: processingTime,
          processingTimeSeconds: Math.round(processingTime / 1000),
          startTime: new Date(startTime).toISOString(),
          endTime: new Date().toISOString(),
        },
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint for current database state
 */
export async function GET() {
  try {
    const payload = await getPayload({ config });

    const [keywordStats, categoryStats, feedStats, articleStats] =
      await Promise.all([
        payload.find({ collection: 'keywords', limit: 1000 }),
        payload.find({ collection: 'categories', limit: 1000 }),
        payload.find({ collection: 'rss-feeds', limit: 1000 }),
        payload.find({ collection: 'articles', limit: 1000 }),
      ]);

    return NextResponse.json({
      success: true,
      message: 'Current database state',
      totals: {
        keywords: keywordStats.totalDocs,
        categories: categoryStats.totalDocs,
        rssFeeds: feedStats.totalDocs,
        articles: articleStats.totalDocs,
      },
      readyForRssProcessing:
        feedStats.totalDocs > 0 && keywordStats.totalDocs > 0,
      adminUrls: {
        articles: 'http://localhost:3001/admin/collections/articles',
        rssFeeds: 'http://localhost:3001/admin/collections/rss-feeds',
        keywords: 'http://localhost:3001/admin/collections/keywords',
        categories: 'http://localhost:3001/admin/collections/categories',
      },
    });
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        message: 'Failed to get database state',
      },
      { status: 500 }
    );
  }
}
