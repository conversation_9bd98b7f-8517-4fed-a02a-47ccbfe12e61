import { headers as getHeaders } from 'next/headers.js';
//import Image from 'next/image';
import { getPayload } from 'payload';
//import React from 'react';
import { fileURLToPath } from 'url';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import config from '@/payload.config';
import './global.css';

export default async function HomePage() {
  //const headers = await getHeaders();
  //const payloadConfig = await config;
  //const payload = await getPayload({ config: payloadConfig });
  //const { user } = await payload.auth({ headers });

  //const fileURL = `vscode://file/${fileURLToPath(import.meta.url)}`;

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8 gap-8">
      <h1 className="text-4xl font-bold font-[family-name:var(--font-merriweather)]">
        <PERSON><PERSON> Blick
      </h1>

      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle className="font-[family-name:var(--font-merriweather)]">
            Card Title
          </CardTitle>
          <CardDescription className="font-[family-name:var(--font-roboto)]">
            This is a description of the card. It provides additional context
            about what the card contains.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="font-[family-name:var(--font-roboto)]">
            This is the main content of the card. You can put any content here
            like text, images, forms, or other components.
          </p>
        </CardContent>
        <CardFooter className="flex justify-between">
          <button
            type="button"
            className="text-sm text-muted-foreground font-[family-name:var(--font-roboto)]"
          >
            Cancel
          </button>
          <button
            type="button"
            className="bg-primary text-primary-foreground px-4 py-2 rounded text-sm font-[family-name:var(--font-roboto)]"
          >
            Save changes
          </button>
        </CardFooter>
      </Card>
    </div>
  );
}
