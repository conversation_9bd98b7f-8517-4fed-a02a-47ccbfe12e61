// storage-adapter-import-placeholder
import { postgresAdapter } from '@payloadcms/db-postgres';
//import { payloadCloudPlugin } from '@payloadcms/payload-cloud';
import { seoPlugin } from '@payloadcms/plugin-seo';
import { lexicalEditor } from '@payloadcms/richtext-lexical';
import { buildConfig } from 'payload';
import sharp from 'sharp';
import Articles from './collections/Articles';
import { Categories } from './collections/Categories';
import Keywords from './collections/Keywords';
import { Media } from './collections/Media';
import ProcessedUrls from './collections/ProcessedUrls';
import RSSFeeds from './collections/RSSFeeds';
import { Users } from './collections/Users';
// Import migrations for production
import { migrations } from './migrations';

export default buildConfig({
  admin: {
    user: Users.slug,
  },
  collections: [
    Users,
    Articles,
    Categories,
    Keywords,
    ProcessedUrls,
    RSSFeeds,
    Media,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URI || '',
    },
    // Follow PayloadCMS best practices - only push in development
    push: process.env.NODE_ENV === 'development',
    // Use migrations in production for safety
    prodMigrations:
      process.env.NODE_ENV === 'production' ? migrations : undefined,
  }),
  sharp,
  plugins: [
    seoPlugin({
      generateTitle: ({ doc }) =>
        doc.germanTab?.germanTitle ||
        doc.englishTab?.enhancedTitle ||
        doc.title,
      generateImage: ({ doc }) => doc.featuredImage,
      generateDescription: ({ doc }) =>
        doc.germanTab?.germanSummary || doc.englishTab?.enhancedSummary,
      generateURL: ({ doc, collectionSlug }) =>
        `https://www.borsenblick.de/${collectionSlug}/${doc?.slug}`,
    }),
  ],
});
