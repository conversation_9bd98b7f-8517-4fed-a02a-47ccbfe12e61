import {
  extractContentEnhanced,
  logFirecrawlSummary,
} from '@/lib/integrations/firecrawl/enhanced-client';
import { parseRSSFeed, type RSSItem } from '@/lib/integrations/rss/parser';
import { createCandidateArticle } from '@/lib/server/create-candidate';
import {
  checkUrlExists,
  createProcessedUrl,
} from '@/lib/server/processed-urls/index';
import {
  getActiveRSSFeeds,
  updateRSSFeedStats,
} from '@/lib/server/rss-feeds/index';
import type { Article, ProcessedUrl } from '@/lib/types';
import type { RssFeed } from '@/payload-types';

interface ProcessingResult {
  success: boolean;
  processed: number;
  accepted: number;
  rejected: number;
  errors: string[];
  details: ProcessingDetail[];
}

interface ProcessingDetail {
  url: string;
  title: string;
  status: 'accepted' | 'rejected' | 'error' | 'rate_limited';
  reason: string;
  articleId?: string;
}

export class RSSProcessingService {
  /**
   * Process all active RSS feeds
   */
  async processAllFeeds(): Promise<ProcessingResult> {
    console.log('🔄 Starting RSS processing for all active feeds...');

    const result: ProcessingResult = {
      success: true,
      processed: 0,
      accepted: 0,
      rejected: 0,
      errors: [],
      details: [],
    };

    try {
      // Get all active RSS feeds from database
      const activeFeeds = await getActiveRSSFeeds();
      console.log(`📡 Found ${activeFeeds.length} active RSS feeds`);

      // Sort feeds by priority (high → medium → low)
      const sortedFeeds = activeFeeds.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return (
          (priorityOrder[b.priority as keyof typeof priorityOrder] || 0) -
          (priorityOrder[a.priority as keyof typeof priorityOrder] || 0)
        );
      });

      for (let i = 0; i < sortedFeeds.length; i++) {
        const feed = sortedFeeds[i];
        console.log(
          `\n📰 [${i + 1}/${sortedFeeds.length}] Processing: ${feed.name}`
        );
        console.log(
          `   Priority: ${feed.priority} | Language: ${feed.language}`
        );
        console.log(`   URL: ${feed.url}`);

        try {
          const feedResult = await this.processSingleFeed(feed);

          // Log feed-specific results
          console.log(
            `   📊 Feed Results: ${feedResult.accepted} accepted, ${feedResult.rejected} rejected, ${feedResult.errors.length} errors`
          );

          // Aggregate results
          result.processed += feedResult.processed;
          result.accepted += feedResult.accepted;
          result.rejected += feedResult.rejected;
          result.details.push(...feedResult.details);

          if (!feedResult.success) {
            result.errors.push(
              `Feed ${feed.name}: ${feedResult.errors.join(', ')}`
            );
          }

          // Update feed statistics
          await updateRSSFeedStats(feed.id.toString(), {
            processed: feedResult.processed,
            accepted: feedResult.accepted,
            articlesFoundSinceLastSuccessful: feedResult.accepted,
            success: feedResult.success && feedResult.errors.length === 0,
            errorMessage:
              feedResult.errors.length > 0
                ? feedResult.errors.join(', ')
                : undefined,
          });
        } catch (error: any) {
          const errorMsg = `Failed to process feed ${feed.name}: ${error.message}`;
          console.error('❌', errorMsg);
          result.errors.push(errorMsg);
          result.success = false;
        }
      }

      console.log('\n🎯 Final Processing Summary:');
      console.log(`   📄 Total items processed: ${result.processed}`);
      console.log(`   ✅ Articles accepted: ${result.accepted}`);
      console.log(`   ❌ Articles rejected: ${result.rejected}`);
      console.log(`   ⚠️  Errors encountered: ${result.errors.length}`);
      console.log(
        `   📈 Success rate: ${result.processed > 0 ? Math.round((result.accepted / result.processed) * 100) : 0}%`
      );

      // Log Firecrawl API usage summary
      logFirecrawlSummary();

      return result;
    } catch (error: any) {
      console.error('❌ RSS processing failed:', error);
      result.success = false;
      result.errors.push(`Global processing error: ${error.message}`);
      return result;
    }
  }

  /**
   * Build feed-specific extraction options for Firecrawl
   */
  private buildFeedSpecificOptions(feed: RssFeed) {
    const options: any = {};

    // Apply feed-specific Firecrawl options if configured
    if ((feed as any).firecrawlOptions) {
      const firecrawlOpts = (feed as any).firecrawlOptions;
      if (firecrawlOpts.removeBase64Images !== undefined) {
        options.removeBase64Images = firecrawlOpts.removeBase64Images;
      }
      if (firecrawlOpts.blockAds !== undefined) {
        options.blockAds = firecrawlOpts.blockAds;
      }
      if (firecrawlOpts.excludeTags && firecrawlOpts.excludeTags.length > 0) {
        options.excludeTags = firecrawlOpts.excludeTags
          .map((item: any) => item.tag)
          .filter(Boolean);
      }
      if (firecrawlOpts.includeTags && firecrawlOpts.includeTags.length > 0) {
        options.includeTags = firecrawlOpts.includeTags
          .map((item: any) => item.tag)
          .filter(Boolean);
      }
    }

    // Apply processing options if configured
    if ((feed as any).processingOptions) {
      const procOpts = (feed as any).processingOptions;
      if (procOpts.customTimeout) {
        options.timeout = procOpts.customTimeout * 1000; // Convert to milliseconds
      }
      if (procOpts.enableStealth) {
        options.forceStealth = true;
      }
    }

    return options;
  }

  /**
   * Process a single RSS feed
   */
  async processSingleFeed(feed: RssFeed): Promise<ProcessingResult> {
    const result: ProcessingResult = {
      success: true,
      processed: 0,
      accepted: 0,
      rejected: 0,
      errors: [],
      details: [],
    };

    try {
      // Parse RSS feed
      console.log(`   🔍 Parsing RSS feed...`);
      const rssItems = await parseRSSFeed(feed.url);
      console.log(`   📄 Found ${rssItems.length} items in feed`);

      let acceptedCount = 0;
      let rejectedCount = 0;
      let errorCount = 0;

      // Apply maxArticlesPerRun limit if configured
      const maxArticles =
        (feed as any).processingOptions?.maxArticlesPerRun || rssItems.length;
      const itemsToProcess = rssItems.slice(0, maxArticles);

      if (maxArticles < rssItems.length) {
        console.log(
          `   🎯 Processing limited to ${maxArticles} articles (feed setting)`
        );
      }

      for (let i = 0; i < itemsToProcess.length; i++) {
        const item = itemsToProcess[i];
        const itemNumber = i + 1;

        try {
          console.log(
            `   📰 [${itemNumber}/${itemsToProcess.length}] Processing: ${item.title?.substring(0, 60)}${item.title && item.title.length > 60 ? '...' : ''}`
          );

          const itemResult = await this.processRSSItem(item, feed);
          result.details.push(itemResult);
          result.processed++;

          if (itemResult.status === 'accepted') {
            result.accepted++;
            acceptedCount++;
            console.log(`      ✅ ACCEPTED`);
          } else if (itemResult.status === 'rejected') {
            result.rejected++;
            rejectedCount++;
            console.log(`      ❌ REJECTED (${itemResult.reason})`);
          } else if (itemResult.status === 'rate_limited') {
            errorCount++;
            console.log(`      ⏳ RATE LIMITED (${itemResult.reason})`);
          } else {
            errorCount++;
            console.log(`      ⚠️  ERROR (${itemResult.reason})`);
          }
        } catch (error: any) {
          const errorMsg = `Failed to process item ${item.title}: ${error.message}`;
          console.error('❌', errorMsg);
          result.errors.push(errorMsg);
          result.details.push({
            url: item.link || 'unknown',
            title: item.title || 'unknown',
            status: 'error',
            reason: error.message,
          });
          errorCount++;
        }

        // Show progress every 10 items for large feeds
        if (itemNumber % 10 === 0 && rssItems.length > 20) {
          console.log(
            `   📊 Progress: ${itemNumber}/${rssItems.length} processed (${acceptedCount} accepted, ${rejectedCount} rejected, ${errorCount} errors)`
          );
        }
      }

      return result;
    } catch (error: any) {
      console.error(`❌ Failed to parse RSS feed ${feed.url}:`, error);
      result.success = false;
      result.errors.push(`RSS parsing error: ${error.message}`);
      return result;
    }
  }

  /**
   * Process a single RSS item through the complete pipeline
   */
  private async processRSSItem(
    item: RSSItem,
    feed: RssFeed
  ): Promise<ProcessingDetail> {
    const url = item.link;
    const title = item.title || 'Untitled';

    if (!url) {
      return {
        url: 'unknown',
        title,
        status: 'error',
        reason: 'No URL provided',
      };
    }

    // Step 1: Check if URL already processed
    const urlExists = await checkUrlExists(url);
    if (urlExists) {
      return {
        url,
        title,
        status: 'rejected',
        reason: 'URL already processed',
      };
    }

    // Step 2: Basic keyword filtering using predefined keywords
    const hasRelevantKeywords = await this.checkBasicKeywords(
      title,
      item.description || ''
    );
    if (!hasRelevantKeywords) {
      // Mark as processed but rejected
      await createProcessedUrl({
        url,
        title,
        feedId: feed.id.toString(),
        status: 'rejected',
        reason: 'No relevant keywords found',
        publicationDate: item.pubDate ? new Date(item.pubDate) : undefined,
      });

      return {
        url,
        title,
        status: 'rejected',
        reason: 'No relevant keywords in title/description',
      };
    }

    // Step 3: Extract full content using Enhanced Firecrawl
    console.log(`      🔍 Extracting content with enhanced client...`);
    const feedOptions = this.buildFeedSpecificOptions(feed);
    const enhancedResult = await extractContentEnhanced(url, feedOptions);

    // Convert enhanced result to legacy format for compatibility
    const contentResult = {
      success: enhancedResult.success,
      content:
        enhancedResult.formats.html || enhancedResult.formats.markdown || '',
      title: enhancedResult.metadata?.title,
      author: enhancedResult.metadata?.author,
      publishedDate: enhancedResult.metadata?.publishDate
        ? new Date(enhancedResult.metadata.publishDate)
        : undefined,
      error:
        enhancedResult.errors.length > 0
          ? enhancedResult.errors.join(', ')
          : undefined,
      metadata: {
        wordCount: enhancedResult.metadata?.wordCount || 0,
        readingTime: enhancedResult.metadata?.readingTime || 0,
      },
    };

    if (!contentResult.success || !contentResult.content) {
      const isRateLimited =
        contentResult.error?.includes('Rate limited') ||
        contentResult.error?.includes('429');

      // For rate limiting, we want to mark as pending retry rather than error
      const status = isRateLimited ? 'pending' : 'error';
      const reason = isRateLimited
        ? 'Rate limited - will retry later'
        : contentResult.error || 'Content extraction failed';

      await createProcessedUrl({
        url,
        title,
        feedId: feed.id.toString(),
        status,
        reason,
        publicationDate: item.pubDate ? new Date(item.pubDate) : undefined,
      });

      if (isRateLimited) {
        console.log(`      ⏳ Rate limited for ${url} - marked for retry`);
      }

      return {
        url,
        title,
        status: isRateLimited ? 'rate_limited' : 'error',
        reason,
      };
    }

    // Step 4: Create candidate article (keywords already matched)
    console.log(`      📝 Creating candidate article`);

    const articleResult = await createCandidateArticle({
      title,
      content: contentResult.content,
      sourceUrl: url,
      sourceFeed: feed.id.toString(),
      publishedDate: item.pubDate ? new Date(item.pubDate) : new Date(),
      author: item.author || undefined,
    });

    // Mark URL as processed and accepted
    await createProcessedUrl({
      url,
      title,
      feedId: feed.id.toString(),
      status: 'accepted',
      reason: 'Keywords matched',
      articleId: articleResult.id,
      publicationDate: item.pubDate ? new Date(item.pubDate) : undefined,
    });

    return {
      url,
      title,
      status: 'accepted',
      reason: 'Keywords matched',
      articleId: articleResult.id,
    };
  }

  /**
   * Basic keyword filtering using predefined keywords from database
   */
  private async checkBasicKeywords(
    title: string,
    description: string
  ): Promise<boolean> {
    try {
      const { getPayload } = await import('payload');
      const config = await import('@payload-config');
      const payload = await getPayload({ config: config.default });

      // Get all active keywords from database
      const allKeywords = await payload.find({
        collection: 'keywords',
        where: {
          isActive: { equals: true },
        },
        limit: 1000,
      });

      const text = `${title} ${description}`.toLowerCase();

      // Check if any predefined keyword (German or English) appears in the text
      return allKeywords.docs.some((dbKeyword: any) => {
        const germanKeyword = dbKeyword.keyword.toLowerCase();
        const englishKeyword = dbKeyword.englishKeyword.toLowerCase();
        return text.includes(germanKeyword) || text.includes(englishKeyword);
      });
    } catch (error: any) {
      console.error(
        '❌ Failed to check keywords from database:',
        error.message
      );
      // Fallback to basic check for common financial terms
      const text = `${title} ${description}`.toLowerCase();
      const fallbackKeywords = [
        'aktien',
        'börse',
        'investment',
        'wirtschaft',
        'finanzen',
        'stock',
        'market',
        'finance',
      ];
      return fallbackKeywords.some(keyword => text.includes(keyword));
    }
  }
}

// Export singleton instance
export const rssProcessingService = new RSSProcessingService();
