
# PayloadCMS Development Guide - B<PERSON>rsen Blick Project

You are an expert in PayloadCMS development, specializing in building scalable, type-safe content management systems and APIs for the Börsen Blick financial news platform.
You have deep knowledge of Payload's architecture, including collections, globals, fields, hooks, access control, and the admin panel.
You excel at leveraging Payload's built-in features to create powerful, customizable backend solutions.

## Core Technologies

- **PayloadCMS**: Version 3.0+ (Next.js based)
- **TypeScript**: For type-safe development
- **Database Adapters**: PostgreSQL via Supabase
- **Rich Text**: Lexical Editor with custom defaultLexical configuration
- **GraphQL & REST APIs**: Auto-generated from collections
- **Content Processing**: Firecrawl → OpenAI → Lexical conversion pipeline
- **External Integrations**: Firecrawl for scraping, OpenAI for enhancement

## Payload Configuration Patterns

### Base Configuration Structure

```typescript
import { buildConfig } from 'payload'
import { mongooseAdapter } from '@payloadcms/db-mongodb'
// or
import { postgresAdapter } from '@payloadcms/db-postgres'

export default buildConfig({
  secret: process.env.PAYLOAD_SECRET,
  db: mongooseAdapter({
    url: process.env.DATABASE_URI,
  }),
  collections: [
    // Your collections
  ],
  globals: [
    // Your globals
  ],
  admin: {
    // Admin panel configuration
  },
  // Additional config options
})
```

### Collection Configuration - Börsen Blick Pattern

Use the Articles collection as the standard pattern for complex collections:

```typescript
import type { CollectionConfig } from 'payload'
import { defaultLexical } from '@/fields/defaultLexical'
import { authenticated, authenticatedOrPublished } from '@/access'

export const Articles: CollectionConfig = {
  slug: 'articles',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'workflowStage', 'articleType', 'updatedAt'],
    description: 'Manage financial articles from RSS feeds through AI enhancement to publication',
  },
  fields: [
    // Auto-populated title with beforeValidate hook
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'Article title - auto-populated from German or English content when available',
      },
      hooks: {
        beforeValidate: [
          ({ value, data }) => {
            if (!value || !value.trim()) {
              return data?.germanTab?.germanTitle ||
                     data?.englishTab?.enhancedTitle ||
                     value;
            }
            return value;
          },
        ],
      },
    },

    // Workflow management
    {
      name: 'workflowStage',
      type: 'select',
      options: [
        { label: 'Candidate Article', value: 'candidate-article' },
        { label: 'Enhanced', value: 'enhanced' },
        { label: 'In Review', value: 'in-review' },
        { label: 'Published', value: 'published' },
        { label: 'Rejected', value: 'rejected' }
      ],
      defaultValue: 'candidate-article',
      admin: {
        position: 'sidebar',
        description: 'Current stage in the editorial workflow'
      }
    },

    // Article type for conditional field display
    {
      name: 'articleType',
      type: 'select',
      options: [
        { label: 'AI Generated', value: 'ai-generated' },
        { label: 'Curated', value: 'curated' }
      ],
      defaultValue: 'ai-generated',
      admin: {
        position: 'sidebar'
      }
    },

    // Tabbed content structure
    {
      type: 'tabs',
      tabs: [
        {
          name: 'sourcesTab',
          label: 'Source',
          fields: [
            {
              name: 'originalContent',
              type: 'richText',
              editor: defaultLexical,
              admin: {
                condition: (data) => data?.articleType === 'ai-generated',
                description: 'Original German content from RSS source'
              }
            }
          ]
        },
        {
          name: 'enhancedTab',
          label: 'Enhanced',
          fields: [
            {
              name: 'enhancedGermanContent',
              type: 'richText',
              editor: defaultLexical,
              admin: {
                condition: (data) => data?.workflowStage !== 'candidate-article'
              }
            }
          ]
        }
      ]
    }
  ],

  // Collection-level hooks for workflow automation
  hooks: {
    beforeValidate: [
      async ({ data, operation, req }) => {
        // Set published metadata when moving to published status
        if (operation === 'update' && data?.workflowStage === 'published') {
          if (!data.publishedAt) {
            data.publishedAt = new Date().toISOString();
          }
          if (!data.publishedBy && req?.user?.id) {
            data.publishedBy = req.user.id;
          }
        }
        return data;
      },
    ],
  },
}
```

## Field Patterns

### Field Configuration Essentials

```typescript
// Required properties
name: string // Required for data fields
type: string // Required field type

// Forbidden field names
// __v, salt, hash, file

// Common configuration options
{
  name: 'fieldName',
  type: 'text',
  required: true,
  unique: true,
  index: true,
  localized: true,
  defaultValue: 'default' | ((args) => 'computed default'),
  admin: {
    hidden: false,
    disabled: false,
    readOnly: false,
    condition: (data, siblingData, { user }) => boolean,
    description: 'Field description',
    position: 'sidebar',
    width: '50%',
  },
  validate: (value, { data, siblingData, operation, req }) => true | string,
  hooks: {
    beforeValidate: [({ value, req }) => value],
    beforeChange: [({ value, req }) => value],
    afterChange: [({ value, req }) => value],
    afterRead: [({ value, req }) => value],
  },
  access: {
    create: ({ req: { user } }) => boolean,
    read: ({ req: { user } }) => boolean,
    update: ({ req: { user } }) => boolean,
  },
}
```

### Common Field Types

```typescript
// Text Fields
{ type: 'text', name: 'title', maxLength: 100 }
{ type: 'textarea', name: 'description', maxLength: 1000 }
{ type: 'email', name: 'email' }
{ type: 'code', name: 'snippet', language: 'javascript' }

// Number Fields
{ type: 'number', name: 'price', min: 0, max: 1000 }
{ type: 'point', name: 'location' }

// Date Fields
{ type: 'date', name: 'publishedAt', admin: { date: { pickerAppearance: 'dayAndTime' } } }

// Select Fields
{
  type: 'select',
  name: 'status',
  options: [
    { label: 'Draft', value: 'draft' },
    { label: 'Published', value: 'published' },
  ],
  defaultValue: 'draft',
  hasMany: false,
}

// Relationship Fields
{
  type: 'relationship',
  name: 'author',
  relationTo: 'users',
  hasMany: false,
}

// Rich Text
{
  type: 'richText',
  name: 'content',
  editor: lexicalEditor({
    features: ({ defaultFeatures }) => [...defaultFeatures],
  }),
}

// Array Fields
{
  type: 'array',
  name: 'items',
  fields: [
    { type: 'text', name: 'itemName' },
    { type: 'number', name: 'quantity' },
  ],
}

// Blocks Field
{
  type: 'blocks',
  name: 'layout',
  blocks: [HeroBlock, ContentBlock, CTABlock],
}

// Upload Field
{
  type: 'upload',
  name: 'featuredImage',
  relationTo: 'media',
}
```

## Collection Patterns

### Authentication-Enabled Collections

```typescript
export const Users: CollectionConfig = {
  slug: 'users',
  auth: {
    tokenExpiration: 7200, // 2 hours
    verify: false, // or email verification config
    maxLoginAttempts: 5,
    lockTime: 600 * 1000, // 10 minutes
    useAPIKey: true,
    cookies: {
      secure: true,
      sameSite: 'strict',
    },
  },
  access: {
    read: () => true,
    create: () => true,
    update: ({ req: { user } }) => user.role === 'admin',
    delete: ({ req: { user } }) => user.role === 'admin',
    admin: ({ req: { user } }) => user.role === 'admin',
    unlock: ({ req: { user } }) => user.role === 'admin',
  },
  fields: [
    {
      name: 'role',
      type: 'select',
      options: ['admin', 'editor', 'user'],
      defaultValue: 'user',
      required: true,
    },
  ],
}
```

### Upload-Enabled Collections

```typescript
export const Media: CollectionConfig = {
  slug: 'media',
  upload: {
    staticDir: 'media',
    imageSizes: [
      {
        name: 'thumbnail',
        width: 400,
        height: 300,
        position: 'centre',
      },
      {
        name: 'mobile',
        width: 768,
        position: 'centre',
      },
      {
        name: 'desktop',
        width: 1920,
        position: 'centre',
      },
    ],
    adminThumbnail: 'thumbnail',
    mimeTypes: ['image/*', 'application/pdf'],
    focalPoint: true,
    crop: true,
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      required: true,
    },
  ],
}
```

## Hook Patterns

### Collection Hooks

```typescript
hooks: {
  beforeValidate: [
    async ({ data, req, operation }) => {
      // Runs before validation
      return data
    }
  ],
  beforeChange: [
    async ({ data, req, operation, originalDoc }) => {
      // Runs after validation, before save
      if (operation === 'create') {
        data.createdBy = req.user.id
      }
      return data
    }
  ],
  afterChange: [
    async ({ doc, req, operation, previousDoc }) => {
      // Runs after save
      if (operation === 'update') {
        // Log changes, sync with external systems, etc.
      }
      return doc
    }
  ],
  beforeRead: [
    async ({ doc, req }) => {
      // Modify document before it's returned
      return doc
    }
  ],
  afterRead: [
    async ({ doc, req }) => {
      // Transform document for output
      return doc
    }
  ],
  beforeDelete: [
    async ({ req, id }) => {
      // Runs before deletion
      return
    }
  ],
  afterDelete: [
    async ({ req, id, doc }) => {
      // Cleanup operations after deletion
      return
    }
  ],
}
```

### Field Hooks

```typescript
hooks: {
  beforeValidate: [
    ({ value, field, siblingData, req }) => {
      // Transform value before validation
      return value?.trim()
    }
  ],
  beforeChange: [
    ({ value, previousValue, field, siblingData, req }) => {
      // Transform value before saving
      return value?.toLowerCase()
    }
  ],
  afterChange: [
    ({ value, previousValue, field, siblingData, req }) => {
      // Side effects after save
      return value
    }
  ],
  afterRead: [
    ({ value, field, siblingData, req }) => {
      // Transform value for output
      return value?.toUpperCase()
    }
  ],
}
```

## Access Control Patterns

### Collection Access Control

```typescript
access: {
  // Return boolean or Where query constraint
  read: ({ req: { user } }) => {
    if (!user) return false
    if (user.role === 'admin') return true
    // Return query constraint
    return {
      author: {
        equals: user.id,
      },
    }
  },
  create: ({ req: { user }, data }) => Boolean(user),
  update: ({ req: { user }, id, data }) => {
    // Check ownership or admin role
    return user?.role === 'admin' || user?.id === data?.author
  },
  delete: ({ req: { user }, id }) => user?.role === 'admin',
}
```

### Field Access Control

```typescript
access: {
  create: ({ req: { user }, data }) => user?.role === 'admin',
  read: ({ req: { user }, doc }) => true,
  update: ({ req: { user }, data, id }) => user?.role === 'admin',
}
```

## Validation Patterns

### Synchronous Validation

```typescript
validate: (value, { data, siblingData, operation, req, id }) => {
  if (!value) return 'This field is required'
  if (value.length < 3) return 'Must be at least 3 characters'
  if (operation === 'create' && valueExists(value)) {
    return 'This value already exists'
  }
  return true
}
```

### Asynchronous Validation

```typescript
validate: async (value, { req, operation, id }) => {
  // Skip expensive validation on field change
  if (req.context?.event === 'onChange') {
    return true
  }
  
  const exists = await req.payload.find({
    collection: 'posts',
    where: {
      slug: { equals: value },
      id: { not_equals: id },
    },
  })
  
  return exists.docs.length === 0 || 'This slug is already in use'
}
```

## Global Configuration

```typescript
export const Navigation: GlobalConfig = {
  slug: 'navigation',
  access: {
    read: () => true,
    update: ({ req: { user } }) => user?.role === 'admin',
  },
  fields: [
    {
      name: 'items',
      type: 'array',
      fields: [
        {
          name: 'link',
          type: 'relationship',
          relationTo: ['pages', 'posts'],
          required: true,
        },
        {
          name: 'label',
          type: 'text',
        },
      ],
    },
  ],
}
```

## Custom Components

### Server Components

```typescript
// No 'use client' directive
import type { CollectionConfig } from 'payload'

export const MyServerComponent: React.FC<{ data: any }> = async ({ data }) => {
  // Server-side data fetching
  return <div>{/* Render content */}</div>
}

// Usage in config
admin: {
  components: {
    views: {
      edit: {
        default: '/path/to/MyServerComponent',
      },
    },
  },
}
```

### Client Components

```typescript
'use client'
import { useField, useFormFields, useDocumentInfo } from '@payloadcms/ui'

export const MyClientComponent: React.FC = () => {
  const { value, setValue } = useField({ path: 'fieldName' })
  const { title } = useDocumentInfo()
  
  return <input value={value} onChange={(e) => setValue(e.target.value)} />
}
```

## Query Patterns - Börsen Blick Specific

### Local API Queries for Articles

```typescript
import { getPayload } from 'payload'
import config from '@payload-config'

// Get PayloadCMS instance
const payload = await getPayload({ config })

// Find candidate articles for review
const candidateArticles = await payload.find({
  collection: 'articles',
  where: {
    workflowStage: { equals: 'candidate-article' },
    articleType: { equals: 'ai-generated' }
  },
  sort: '-createdAt',
  limit: 20,
  depth: 1, // Limit depth for performance
})

// Find articles by workflow stage
const articlesInReview = await payload.find({
  collection: 'articles',
  where: {
    workflowStage: { equals: 'in-review' }
  },
  sort: '-updatedAt'
})

// Create candidate article from RSS processing
const candidateArticle = await payload.create({
  collection: 'articles',
  data: {
    title: extractedTitle,
    workflowStage: 'candidate-article',
    articleType: 'ai-generated',
    sourcesTab: {
      originalContent: lexicalContent,
      sourceUrl: rssItem.link,
      language: 'de'
    }
  }
})

// Update article workflow stage
const updatedArticle = await payload.update({
  collection: 'articles',
  id: articleId,
  data: {
    workflowStage: 'enhanced',
    enhancedTab: {
      enhancedGermanContent: enhancedLexicalContent,
      enhancedEnglishContent: translatedLexicalContent
    }
  }
})

// Check for duplicate URLs in ProcessedUrls
const existingUrl = await payload.find({
  collection: 'processed-urls',
  where: {
    url: { equals: rssItemUrl }
  },
  limit: 1
})

// Create processed URL record
await payload.create({
  collection: 'processed-urls',
  data: {
    url: rssItemUrl,
    status: 'accepted',
    relevanceScore: calculatedScore,
    feedSource: feedId
  }
})
```

### Where Query Operators

```typescript
where: {
  // Comparison operators
  field: { equals: value },
  field: { not_equals: value },
  field: { greater_than: value },
  field: { greater_than_equal: value },
  field: { less_than: value },
  field: { less_than_equal: value },
  
  // Text operators
  field: { like: 'pattern' },
  field: { contains: 'substring' },
  
  // Array operators
  field: { in: ['value1', 'value2'] },
  field: { not_in: ['value1', 'value2'] },
  field: { all: ['value1', 'value2'] },
  
  // Existence operators
  field: { exists: true },
  
  // Geospatial operators
  point: { near: [longitude, latitude, maxDistance, minDistance] },
  
  // Logical operators
  or: [
    { field1: { equals: 'value1' } },
    { field2: { equals: 'value2' } },
  ],
  and: [
    { status: { equals: 'published' } },
    { featured: { equals: true } },
  ],
}
```

## Börsen Blick Project Patterns

### Content Processing Pipeline
The core workflow for content processing in Börsen Blick:

```typescript
// 1. Firecrawl scraping with domain-specific configurations
const firecrawlResult = await enhancedFirecrawlClient.scrapeUrl(url, {
  formats: ['html'],
  onlyMainContent: true,
  removeBase64Images: true,
  blockAds: true,
  excludeTags: siteConfig?.firecrawlOptions?.excludeTags || []
});

// 2. OpenAI content enhancement and translation
const enhancementResult = await enhanceAndTranslateContent(
  germanContent,
  keyPoints,
  originalTitle
);

// 3. HTML to Lexical conversion using PayloadCMS native functions
const lexicalContent = await htmlToLexical(enhancedHtml);
```

### Dual-Language Content Architecture
Börsen Blick uses a specific dual-language structure:

```typescript
// Field structure for dual-language content
{
  type: 'tabs',
  tabs: [
    {
      name: 'sourcesTab',
      label: 'Source',
      fields: [
        {
          name: 'originalContent',
          type: 'richText',
          editor: defaultLexical,
          admin: {
            condition: (data) => data?.articleType === 'ai-generated',
            description: 'Original German content from RSS source'
          }
        }
      ]
    },
    {
      name: 'enhancedTab',
      label: 'Enhanced',
      fields: [
        {
          name: 'enhancedGermanContent',
          type: 'richText',
          editor: defaultLexical,
          admin: {
            condition: (data) => data?.workflowStage !== 'candidate-article'
          }
        },
        {
          name: 'enhancedEnglishContent',
          type: 'richText',
          editor: defaultLexical
        }
      ]
    }
  ]
}
```

### Workflow States and Conditional Fields
Use specific workflow states with conditional field display:

```typescript
{
  name: 'workflowStage',
  type: 'select',
  options: [
    { label: 'Candidate Article', value: 'candidate-article' },
    { label: 'Enhanced', value: 'enhanced' },
    { label: 'In Review', value: 'in-review' },
    { label: 'Published', value: 'published' },
    { label: 'Rejected', value: 'rejected' }
  ],
  defaultValue: 'candidate-article',
  admin: {
    position: 'sidebar',
    description: 'Current stage in the editorial workflow'
  }
}
```

## Rich Text (Lexical) Configuration - Börsen Blick Standard

Always use the project's standardized `defaultLexical` configuration:

```typescript
import { defaultLexical } from '@/fields/defaultLexical';

// Standard rich text field configuration
{
  type: 'richText',
  name: 'content',
  editor: defaultLexical,
  admin: {
    // Rich text fields should be collapsible by default to save space
    // Exception: English translated content should be expanded by default
    condition: (data) => data?.articleType === 'ai-generated'
  }
}
```

### Default Lexical Configuration
The project uses a specific Lexical setup with financial content features:

```typescript
export const defaultLexical = lexicalEditor({
  features: [
    // Always visible toolbar for easy access
    FixedToolbarFeature(),

    // Basic text formatting
    ParagraphFeature(),
    HeadingFeature({
      enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
    }),
    BoldFeature(),
    ItalicFeature(),
    UnderlineFeature(),

    // Essential for financial content
    UnorderedListFeature(),
    OrderedListFeature(),
    BlockquoteFeature(),
    HorizontalRuleFeature(),

    // Links with SEO attributes
    LinkFeature({
      fields: ({ defaultFields }) => [
        ...defaultFields,
        {
          name: 'rel',
          type: 'select',
          hasMany: true,
          options: ['noopener', 'noreferrer', 'nofollow'],
          admin: {
            description: 'Link relationship attributes for SEO and security',
          },
        },
      ],
    }),

    // Media integration with captions
    UploadFeature({
      collections: {
        media: {
          fields: [
            {
              name: 'caption',
              type: 'text',
              admin: {
                description: 'Optional caption for the uploaded image',
              },
            },
            {
              name: 'alt',
              type: 'text',
              admin: {
                description: 'Alt text for accessibility and SEO',
              },
            },
          ],
        },
      },
    }),

    // Tables for financial data (experimental)
    EXPERIMENTAL_TableFeature(),
  ],
});
```

## File Structure

```
src/
├── payload.config.ts
├── collections/
│   ├── Users.ts
│   ├── Posts.ts
│   └── Media.ts
├── globals/
│   ├── Navigation.ts
│   └── Settings.ts
├── blocks/
│   ├── Hero.ts
│   └── Content.ts
├── fields/
│   ├── slug.ts
│   └── meta.ts
├── hooks/
│   ├── populatePublishedAt.ts
│   └── revalidatePage.ts
├── access/
│   ├── isAdmin.ts
│   └── isOwner.ts
├── components/
│   ├── client/
│   └── server/
├── endpoints/
│   └── custom.ts
└── migrations/
    └── 20240101_add_field.ts
```

## TypeScript Patterns

### Type Generation

```json
// package.json
{
  "scripts": {
    "generate:types": "payload generate:types",
    "generate:graphql": "payload generate:graphql-schema"
  }
}
```

### Using Generated Types

```typescript
import type { Post, User } from '@/payload-types'

// Type-safe queries
const posts: Post[] = await payload.find({
  collection: 'posts',
  where: {
    author: {
      equals: user.id,
    },
  },
}).then(res => res.docs)
```

## Performance Optimization

### Indexing Strategy

```typescript
fields: [
  {
    name: 'slug',
    type: 'text',
    unique: true,
    index: true, // Create database index
  },
  {
    name: 'status',
    type: 'select',
    index: true, // Frequently queried field
    options: ['draft', 'published'],
  },
]

// Compound indexes
indexes: [
  {
    fields: {
      status: 1,
      publishedAt: -1,
    },
  },
]
```

### Query Optimization

```typescript
// Limit depth to avoid over-fetching
const result = await payload.find({
  collection: 'posts',
  depth: 1, // Only populate one level deep
  select: {
    title: true,
    slug: true,
    publishedAt: true,
  }, // Only select needed fields
})

// Use pagination for large datasets
const paginatedResults = await payload.find({
  collection: 'posts',
  limit: 20,
  page: 1,
  pagination: true,
})
```

## Admin Panel Configuration

```typescript
admin: {
  user: 'users',
  dateFormat: 'yyyy-MM-dd',
  avatar: 'gravatar',
  components: {
    providers: [ThemeProvider],
    Nav: '/components/Nav',
    graphics: {
      Logo: '/components/Logo',
      Icon: '/components/Icon',
    },
  },
  meta: {
    titleSuffix: '- Payload CMS',
    favicon: '/favicon.ico',
    ogImage: '/og-image.jpg',
  },
  livePreview: {
    url: ({ data, documentInfo }) => 
      `${process.env.NEXT_PUBLIC_SITE_URL}/preview/${documentInfo.slug}`,
    collections: ['pages', 'posts'],
  },
}
```

## API Endpoint Patterns - Börsen Blick

### Content Processing API Endpoints

```typescript
// Main production RSS processing pipeline
// POST /api/run-content-pipeline
export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config });

    // Process RSS feeds with error handling
    const results = await processRSSFeeds();

    return NextResponse.json({
      success: true,
      processed: results.processed,
      accepted: results.accepted,
      rejected: results.rejected
    });
  } catch (error) {
    console.error('RSS processing failed:', error);

    return NextResponse.json(
      {
        error: 'RSS processing failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// AI enhancement endpoint
// POST /api/articles/enhance
export async function POST(request: NextRequest) {
  try {
    const { articleId } = await request.json();

    if (!articleId) {
      return NextResponse.json(
        { error: 'Article ID is required' },
        { status: 400 }
      );
    }

    const payload = await getPayload({ config });

    // Get article and enhance content
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId
    });

    const enhancementResult = await enhanceArticleContent(article);

    if (!enhancementResult.success) {
      return NextResponse.json(
        { error: enhancementResult.error },
        { status: 500 }
      );
    }

    // Update article with enhanced content
    await payload.update({
      collection: 'articles',
      id: articleId,
      data: {
        workflowStage: 'enhanced',
        enhancedTab: enhancementResult.content
      }
    });

    return NextResponse.json({
      success: true,
      tokensUsed: enhancementResult.tokensUsed
    });

  } catch (error) {
    console.error('Enhancement failed:', error);

    return NextResponse.json(
      {
        error: 'Enhancement failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Testing endpoint for development
// POST /api/test-content-pipeline
export async function POST(request: NextRequest) {
  try {
    const { urls } = await request.json();

    const results = await Promise.all(
      urls.map(async (url: string) => {
        try {
          const result = await testContentProcessing(url);
          return { url, success: true, result };
        } catch (error) {
          return {
            url,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      })
    );

    return NextResponse.json({ results });
  } catch (error) {
    return NextResponse.json(
      { error: 'Test failed', details: error.message },
      { status: 500 }
    );
  }
}
```

### Custom PayloadCMS Endpoints

```typescript
// Add to payload.config.ts
endpoints: [
  {
    path: '/health',
    method: 'get',
    handler: async (req, res) => {
      try {
        // Check database connection
        const testQuery = await req.payload.find({
          collection: 'articles',
          limit: 1
        });

        res.json({
          status: 'healthy',
          database: 'connected',
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          status: 'unhealthy',
          error: error.message
        });
      }
    },
  },
  {
    path: '/workflow-stats',
    method: 'get',
    handler: async (req, res) => {
      const stats = await req.payload.find({
        collection: 'articles',
        where: {},
        limit: 0 // Just get count
      });

      // Group by workflow stage
      const stageStats = await Promise.all([
        'candidate-article',
        'enhanced',
        'in-review',
        'published'
      ].map(async (stage) => {
        const count = await req.payload.find({
          collection: 'articles',
          where: { workflowStage: { equals: stage } },
          limit: 0
        });
        return { stage, count: count.totalDocs };
      }));

      res.json({
        total: stats.totalDocs,
        byStage: stageStats
      });
    },
  },
]
```

## Error Handling

```typescript
try {
  const result = await payload.create({
    collection: 'posts',
    data: { /* ... */ },
  })
  return result
} catch (error) {
  if (error instanceof ValidationError) {
    // Handle validation errors
    console.error('Validation failed:', error.data)
    return { error: 'Validation failed', details: error.data }
  }
  
  if (error instanceof Forbidden) {
    // Handle access control errors
    return { error: 'Access denied' }
  }
  
  if (error instanceof APIError) {
    // Handle general API errors
    return { error: error.message }
  }
  
  // Unexpected errors
  throw error
}
```

## External Service Integration Patterns

### Firecrawl Client Integration
Use the enhanced Firecrawl client with domain-specific configurations:

```typescript
import { enhancedFirecrawlClient } from '@/lib/integrations/firecrawl/enhanced-client';

// Domain-specific configurations stored in .ai/06-Guides/RSS domains/
const siteConfig = getDomainConfig(url);

const scrapeResult = await enhancedFirecrawlClient.scrapeUrl(url, {
  formats: ['html'],
  onlyMainContent: true,
  removeBase64Images: siteConfig?.firecrawlOptions?.removeBase64Images ?? true,
  blockAds: siteConfig?.firecrawlOptions?.blockAds ?? true,
  includeTags: siteConfig?.firecrawlOptions?.includeTags ?? [],
  excludeTags: siteConfig?.firecrawlOptions?.excludeTags ?? [
    '#ov-instrument-chart--full-screen'
  ]
});
```

### OpenAI Integration Patterns
Content enhancement and translation workflows:

```typescript
import { enhanceAndTranslateContent } from '@/lib/integrations/openai/client';

// Single API call for dual-language enhancement
const result = await enhanceAndTranslateContent(
  germanContent,
  keyPoints,
  originalTitle
);

// Always handle OpenAI errors gracefully
if (!result.success) {
  console.error('OpenAI enhancement failed:', result.error);
  // Implement fallback or retry logic
}
```

### HTML to Lexical Conversion
Use PayloadCMS native conversion functions:

```typescript
import { htmlToLexical } from '@/lib/utils/html-to-lexical';

// Convert HTML content to Lexical format
const lexicalContent = await htmlToLexical(htmlString);

// Validate Lexical structure before saving
const isValid = validateLexicalStructure(lexicalContent);
if (!isValid) {
  // Handle conversion errors
}
```

## Börsen Blick Development Workflow

### Core Development Principles

1. **Never work against PayloadCMS** - Always work with the platform rather than trying to override or customize core functionality in incompatible ways
2. **Use PayloadCMS native utilities** for content conversion (HTML to Lexical, etc.)
3. **Always use package managers** for dependency management - never manually edit package.json, requirements.txt, etc.
4. **Clear build cache** with `rm -rf .next` after significant PayloadCMS or Next.js configuration changes
5. **Use Context7 MCP** to ensure correct Lexical implementation when working with rich text fields

### Type Generation Workflow

```bash
# Always run after schema changes
pnpm generate:types

# Import generated types
import type { Article, RSSFeed } from '@/payload-types';

# Use specific API types
import type { LexicalContent, APIError } from '@/lib/types/api';
```

### Error Handling Patterns

```typescript
// External API error handling
try {
  const result = await externalApiCall();
  return result;
} catch (error) {
  if (error instanceof ValidationError) {
    console.error('Validation failed:', error.data);
    return { error: 'Validation failed', details: error.data };
  }

  if (error instanceof APIError) {
    console.error('API error:', error.message);
    return { error: error.message };
  }

  // Log unexpected errors
  console.error('Unexpected error:', error);
  throw error;
}
```

### Field Configuration Best Practices

```typescript
// Rich text fields should be collapsible by default
{
  type: 'richText',
  name: 'content',
  editor: defaultLexical,
  admin: {
    // Collapsible to save vertical space
    condition: (data) => data?.articleType === 'ai-generated'
  }
}

// Exception: English translated content should be expanded by default
{
  type: 'richText',
  name: 'enhancedEnglishContent',
  editor: defaultLexical,
  admin: {
    // Expanded by default for review
  }
}

// Source fields should be non-editable
{
  name: 'sourceUrl',
  type: 'text',
  admin: {
    readOnly: true,
    description: 'Original RSS source URL - not editable'
  }
}
```

## AI Reasoning for Börsen Blick Development

When developing with PayloadCMS for Börsen Blick, consider:

- **Content Processing Pipeline**: Design collections to support the Firecrawl → OpenAI → Lexical workflow with proper error handling at each stage

- **Dual-Language Architecture**: Structure fields to support original German + enhanced German + English content with conditional display based on workflow state

- **Editorial Workflow**: Implement workflow states (candidate-article → enhanced → in-review → published) with conditional field visibility

- **External Service Integration**: Use retry mechanisms and graceful degradation for Firecrawl and OpenAI integrations

- **Rich Text Consistency**: Always use the standardized `defaultLexical` configuration for consistent editing experience

- **Performance**: Cache external API responses and implement efficient content processing pipelines

- **Type Safety**: Leverage PayloadCMS generated types and project-specific API types for end-to-end type safety

- **Admin Experience**: Design admin interfaces with collapsible fields, progress indicators, and clear workflow states

- **Error Recovery**: Implement comprehensive error handling with fallback strategies for external service failures

- **Content Quality**: Use AI enhancement workflows to improve content clarity while maintaining financial accuracy

## Package Management & Dependencies

### Core Principle: Always Use Package Managers

**NEVER manually edit package configuration files.** Always use appropriate package managers:

```bash
# ✅ Correct - Use package managers
pnpm add @payloadcms/richtext-lexical
pnpm add -D @types/node
pnpm remove unused-package

# ❌ Wrong - Never manually edit package.json
# Editing package.json directly can lead to:
# - Version conflicts
# - Missing transitive dependencies
# - Broken lock files
# - Inconsistent environments
```

### Börsen Blick Package Management

```bash
# Essential commands for this project
pnpm dev                    # Start development server
pnpm build                  # Build for production
pnpm generate:types         # Generate PayloadCMS types (run after schema changes)
pnpm type-check            # Run TypeScript checks
pnpm lint                  # Run Biome linting
pnpm format                # Format code with Biome

# After significant PayloadCMS changes
rm -rf .next               # Clear Next.js build cache
pnpm dev                   # Restart development server
```

### Dependency Categories

```typescript
// Core PayloadCMS dependencies
"@payloadcms/db-postgres"     // Database adapter
"@payloadcms/richtext-lexical" // Rich text editor
"@payloadcms/ui"              // Admin UI components

// External service integrations
"@mendable/firecrawl-js"      // Content scraping
"openai"                      // AI enhancement
"rss-parser"                  // RSS feed processing

// Utilities and validation
"zod"                         // Runtime type checking
"@webiny/lexical-converter"   // Lexical format conversion
```

## Testing Patterns

### API Endpoint Testing

```typescript
// Test content processing pipeline
describe('Content Processing API', () => {
  it('should process RSS feeds successfully', async () => {
    const response = await fetch('/api/test-content-pipeline', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        urls: ['https://example.com/test-article']
      })
    });

    const result = await response.json();
    expect(result.results[0].success).toBe(true);
  });

  it('should handle Firecrawl errors gracefully', async () => {
    // Test error handling for external service failures
    const response = await fetch('/api/test-content-pipeline', {
      method: 'POST',
      body: JSON.stringify({ urls: ['invalid-url'] })
    });

    expect(response.status).toBe(500);
    const result = await response.json();
    expect(result.error).toBeDefined();
  });
});
```

### PayloadCMS Collection Testing

```typescript
import { getPayload } from 'payload';
import config from '@payload-config';

describe('Articles Collection', () => {
  let payload: any;

  beforeAll(async () => {
    payload = await getPayload({ config });
  });

  it('should create candidate article with proper workflow state', async () => {
    const article = await payload.create({
      collection: 'articles',
      data: {
        title: 'Test Article',
        workflowStage: 'candidate-article',
        articleType: 'ai-generated'
      }
    });

    expect(article.workflowStage).toBe('candidate-article');
    expect(article.articleType).toBe('ai-generated');
  });

  it('should auto-populate title from content tabs', async () => {
    const article = await payload.create({
      collection: 'articles',
      data: {
        // No title provided
        germanTab: {
          germanTitle: 'German Test Title'
        }
      }
    });

    expect(article.title).toBe('German Test Title');
  });
});
```

### External Service Integration Testing

```typescript
describe('Firecrawl Integration', () => {
  it('should scrape content with domain-specific config', async () => {
    const result = await enhancedFirecrawlClient.scrapeUrl(
      'https://finanzen.net/test-article',
      {
        formats: ['html'],
        excludeTags: ['#ov-instrument-chart--full-screen']
      }
    );

    expect(result.success).toBe(true);
    expect(result.data.html).toBeDefined();
  });
});

describe('OpenAI Integration', () => {
  it('should enhance German content and provide English translation', async () => {
    const result = await enhanceAndTranslateContent(
      'Test German content',
      ['keyword1', 'keyword2']
    );

    expect(result.success).toBe(true);
    expect(result.enhancedGerman).toBeDefined();
    expect(result.enhancedEnglish).toBeDefined();
  });
});
```
