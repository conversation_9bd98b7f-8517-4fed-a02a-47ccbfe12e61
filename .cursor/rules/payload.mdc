
# PayloadCMS Development Guide

You are an expert in PayloadCMS development, specializing in building scalable, type-safe content management systems and APIs.
You have deep knowledge of Payload's architecture, including collections, globals, fields, hooks, access control, and the admin panel.
You excel at leveraging Payload's built-in features to create powerful, customizable backend solutions.

## Core Technologies

- **PayloadCMS**: Version 3.0+ (Next.js based)
- **TypeScript**: For type-safe development
- **Database Adapters**: MongoDB or Postgres
- **Rich Text**: Lexical Editor
- **GraphQL & REST APIs**: Auto-generated from collections

## Payload Configuration Patterns

### Base Configuration Structure

```typescript
import { buildConfig } from 'payload'
import { mongooseAdapter } from '@payloadcms/db-mongodb'
// or
import { postgresAdapter } from '@payloadcms/db-postgres'

export default buildConfig({
  secret: process.env.PAYLOAD_SECRET,
  db: mongooseAdapter({
    url: process.env.DATABASE_URI,
  }),
  collections: [
    // Your collections
  ],
  globals: [
    // Your globals
  ],
  admin: {
    // Admin panel configuration
  },
  // Additional config options
})
```

### Collection Configuration

```typescript
import type { CollectionConfig } from 'payload'

export const Posts: CollectionConfig = {
  slug: 'posts',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'status', 'createdAt'],
    listSearchableFields: ['title', 'slug', 'content'],
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => Boolean(user),
    update: ({ req: { user } }) => Boolean(user),
    delete: ({ req: { user } }) => Boolean(user?.roles?.includes('admin')),
  },
  fields: [
    // Field definitions
  ],
  hooks: {
    // Collection hooks
  },
}
```

## Field Patterns

### Field Configuration Essentials

```typescript
// Required properties
name: string // Required for data fields
type: string // Required field type

// Forbidden field names
// __v, salt, hash, file

// Common configuration options
{
  name: 'fieldName',
  type: 'text',
  required: true,
  unique: true,
  index: true,
  localized: true,
  defaultValue: 'default' | ((args) => 'computed default'),
  admin: {
    hidden: false,
    disabled: false,
    readOnly: false,
    condition: (data, siblingData, { user }) => boolean,
    description: 'Field description',
    position: 'sidebar',
    width: '50%',
  },
  validate: (value, { data, siblingData, operation, req }) => true | string,
  hooks: {
    beforeValidate: [({ value, req }) => value],
    beforeChange: [({ value, req }) => value],
    afterChange: [({ value, req }) => value],
    afterRead: [({ value, req }) => value],
  },
  access: {
    create: ({ req: { user } }) => boolean,
    read: ({ req: { user } }) => boolean,
    update: ({ req: { user } }) => boolean,
  },
}
```

### Common Field Types

```typescript
// Text Fields
{ type: 'text', name: 'title', maxLength: 100 }
{ type: 'textarea', name: 'description', maxLength: 1000 }
{ type: 'email', name: 'email' }
{ type: 'code', name: 'snippet', language: 'javascript' }

// Number Fields
{ type: 'number', name: 'price', min: 0, max: 1000 }
{ type: 'point', name: 'location' }

// Date Fields
{ type: 'date', name: 'publishedAt', admin: { date: { pickerAppearance: 'dayAndTime' } } }

// Select Fields
{
  type: 'select',
  name: 'status',
  options: [
    { label: 'Draft', value: 'draft' },
    { label: 'Published', value: 'published' },
  ],
  defaultValue: 'draft',
  hasMany: false,
}

// Relationship Fields
{
  type: 'relationship',
  name: 'author',
  relationTo: 'users',
  hasMany: false,
}

// Rich Text
{
  type: 'richText',
  name: 'content',
  editor: lexicalEditor({
    features: ({ defaultFeatures }) => [...defaultFeatures],
  }),
}

// Array Fields
{
  type: 'array',
  name: 'items',
  fields: [
    { type: 'text', name: 'itemName' },
    { type: 'number', name: 'quantity' },
  ],
}

// Blocks Field
{
  type: 'blocks',
  name: 'layout',
  blocks: [HeroBlock, ContentBlock, CTABlock],
}

// Upload Field
{
  type: 'upload',
  name: 'featuredImage',
  relationTo: 'media',
}
```

## Collection Patterns

### Authentication-Enabled Collections

```typescript
export const Users: CollectionConfig = {
  slug: 'users',
  auth: {
    tokenExpiration: 7200, // 2 hours
    verify: false, // or email verification config
    maxLoginAttempts: 5,
    lockTime: 600 * 1000, // 10 minutes
    useAPIKey: true,
    cookies: {
      secure: true,
      sameSite: 'strict',
    },
  },
  access: {
    read: () => true,
    create: () => true,
    update: ({ req: { user } }) => user.role === 'admin',
    delete: ({ req: { user } }) => user.role === 'admin',
    admin: ({ req: { user } }) => user.role === 'admin',
    unlock: ({ req: { user } }) => user.role === 'admin',
  },
  fields: [
    {
      name: 'role',
      type: 'select',
      options: ['admin', 'editor', 'user'],
      defaultValue: 'user',
      required: true,
    },
  ],
}
```

### Upload-Enabled Collections

```typescript
export const Media: CollectionConfig = {
  slug: 'media',
  upload: {
    staticDir: 'media',
    imageSizes: [
      {
        name: 'thumbnail',
        width: 400,
        height: 300,
        position: 'centre',
      },
      {
        name: 'mobile',
        width: 768,
        position: 'centre',
      },
      {
        name: 'desktop',
        width: 1920,
        position: 'centre',
      },
    ],
    adminThumbnail: 'thumbnail',
    mimeTypes: ['image/*', 'application/pdf'],
    focalPoint: true,
    crop: true,
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      required: true,
    },
  ],
}
```

## Hook Patterns

### Collection Hooks

```typescript
hooks: {
  beforeValidate: [
    async ({ data, req, operation }) => {
      // Runs before validation
      return data
    }
  ],
  beforeChange: [
    async ({ data, req, operation, originalDoc }) => {
      // Runs after validation, before save
      if (operation === 'create') {
        data.createdBy = req.user.id
      }
      return data
    }
  ],
  afterChange: [
    async ({ doc, req, operation, previousDoc }) => {
      // Runs after save
      if (operation === 'update') {
        // Log changes, sync with external systems, etc.
      }
      return doc
    }
  ],
  beforeRead: [
    async ({ doc, req }) => {
      // Modify document before it's returned
      return doc
    }
  ],
  afterRead: [
    async ({ doc, req }) => {
      // Transform document for output
      return doc
    }
  ],
  beforeDelete: [
    async ({ req, id }) => {
      // Runs before deletion
      return
    }
  ],
  afterDelete: [
    async ({ req, id, doc }) => {
      // Cleanup operations after deletion
      return
    }
  ],
}
```

### Field Hooks

```typescript
hooks: {
  beforeValidate: [
    ({ value, field, siblingData, req }) => {
      // Transform value before validation
      return value?.trim()
    }
  ],
  beforeChange: [
    ({ value, previousValue, field, siblingData, req }) => {
      // Transform value before saving
      return value?.toLowerCase()
    }
  ],
  afterChange: [
    ({ value, previousValue, field, siblingData, req }) => {
      // Side effects after save
      return value
    }
  ],
  afterRead: [
    ({ value, field, siblingData, req }) => {
      // Transform value for output
      return value?.toUpperCase()
    }
  ],
}
```

## Access Control Patterns

### Collection Access Control

```typescript
access: {
  // Return boolean or Where query constraint
  read: ({ req: { user } }) => {
    if (!user) return false
    if (user.role === 'admin') return true
    // Return query constraint
    return {
      author: {
        equals: user.id,
      },
    }
  },
  create: ({ req: { user }, data }) => Boolean(user),
  update: ({ req: { user }, id, data }) => {
    // Check ownership or admin role
    return user?.role === 'admin' || user?.id === data?.author
  },
  delete: ({ req: { user }, id }) => user?.role === 'admin',
}
```

### Field Access Control

```typescript
access: {
  create: ({ req: { user }, data }) => user?.role === 'admin',
  read: ({ req: { user }, doc }) => true,
  update: ({ req: { user }, data, id }) => user?.role === 'admin',
}
```

## Validation Patterns

### Synchronous Validation

```typescript
validate: (value, { data, siblingData, operation, req, id }) => {
  if (!value) return 'This field is required'
  if (value.length < 3) return 'Must be at least 3 characters'
  if (operation === 'create' && valueExists(value)) {
    return 'This value already exists'
  }
  return true
}
```

### Asynchronous Validation

```typescript
validate: async (value, { req, operation, id }) => {
  // Skip expensive validation on field change
  if (req.context?.event === 'onChange') {
    return true
  }
  
  const exists = await req.payload.find({
    collection: 'posts',
    where: {
      slug: { equals: value },
      id: { not_equals: id },
    },
  })
  
  return exists.docs.length === 0 || 'This slug is already in use'
}
```

## Global Configuration

```typescript
export const Navigation: GlobalConfig = {
  slug: 'navigation',
  access: {
    read: () => true,
    update: ({ req: { user } }) => user?.role === 'admin',
  },
  fields: [
    {
      name: 'items',
      type: 'array',
      fields: [
        {
          name: 'link',
          type: 'relationship',
          relationTo: ['pages', 'posts'],
          required: true,
        },
        {
          name: 'label',
          type: 'text',
        },
      ],
    },
  ],
}
```

## Custom Components

### Server Components

```typescript
// No 'use client' directive
import type { CollectionConfig } from 'payload'

export const MyServerComponent: React.FC<{ data: any }> = async ({ data }) => {
  // Server-side data fetching
  return <div>{/* Render content */}</div>
}

// Usage in config
admin: {
  components: {
    views: {
      edit: {
        default: '/path/to/MyServerComponent',
      },
    },
  },
}
```

### Client Components

```typescript
'use client'
import { useField, useFormFields, useDocumentInfo } from '@payloadcms/ui'

export const MyClientComponent: React.FC = () => {
  const { value, setValue } = useField({ path: 'fieldName' })
  const { title } = useDocumentInfo()
  
  return <input value={value} onChange={(e) => setValue(e.target.value)} />
}
```

## Query Patterns

### Local API Queries

```typescript
// Find documents
const posts = await payload.find({
  collection: 'posts',
  where: {
    status: { equals: 'published' },
    publishedAt: { less_than: new Date() },
  },
  sort: '-publishedAt',
  limit: 10,
  page: 1,
  depth: 2,
})

// Find by ID
const post = await payload.findByID({
  collection: 'posts',
  id: 'post-id',
  depth: 2,
})

// Create document
const newPost = await payload.create({
  collection: 'posts',
  data: {
    title: 'New Post',
    content: 'Content...',
  },
})

// Update document
const updated = await payload.update({
  collection: 'posts',
  id: 'post-id',
  data: {
    status: 'published',
  },
})

// Delete document
await payload.delete({
  collection: 'posts',
  id: 'post-id',
})
```

### Where Query Operators

```typescript
where: {
  // Comparison operators
  field: { equals: value },
  field: { not_equals: value },
  field: { greater_than: value },
  field: { greater_than_equal: value },
  field: { less_than: value },
  field: { less_than_equal: value },
  
  // Text operators
  field: { like: 'pattern' },
  field: { contains: 'substring' },
  
  // Array operators
  field: { in: ['value1', 'value2'] },
  field: { not_in: ['value1', 'value2'] },
  field: { all: ['value1', 'value2'] },
  
  // Existence operators
  field: { exists: true },
  
  // Geospatial operators
  point: { near: [longitude, latitude, maxDistance, minDistance] },
  
  // Logical operators
  or: [
    { field1: { equals: 'value1' } },
    { field2: { equals: 'value2' } },
  ],
  and: [
    { status: { equals: 'published' } },
    { featured: { equals: true } },
  ],
}
```

## Rich Text (Lexical) Configuration

```typescript
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { 
  BlocksFeature, 
  LinkFeature, 
  UploadFeature,
  HeadingFeature,
  FixedToolbarFeature,
} from '@payloadcms/richtext-lexical'

{
  type: 'richText',
  name: 'content',
  editor: lexicalEditor({
    features: ({ defaultFeatures, rootFeatures }) => [
      ...defaultFeatures,
      HeadingFeature({ levels: [1, 2, 3, 4] }),
      LinkFeature({
        fields: [
          {
            name: 'rel',
            type: 'select',
            hasMany: true,
            options: ['noopener', 'noreferrer', 'nofollow'],
          },
        ],
      }),
      UploadFeature({
        collections: {
          media: {
            fields: [
              {
                name: 'caption',
                type: 'text',
              },
            ],
          },
        },
      }),
      BlocksFeature({
        blocks: [CallToAction, Quote, CodeBlock],
      }),
      FixedToolbarFeature(),
    ],
  }),
}
```

## File Structure

```
src/
├── payload.config.ts
├── collections/
│   ├── Users.ts
│   ├── Posts.ts
│   └── Media.ts
├── globals/
│   ├── Navigation.ts
│   └── Settings.ts
├── blocks/
│   ├── Hero.ts
│   └── Content.ts
├── fields/
│   ├── slug.ts
│   └── meta.ts
├── hooks/
│   ├── populatePublishedAt.ts
│   └── revalidatePage.ts
├── access/
│   ├── isAdmin.ts
│   └── isOwner.ts
├── components/
│   ├── client/
│   └── server/
├── endpoints/
│   └── custom.ts
└── migrations/
    └── 20240101_add_field.ts
```

## TypeScript Patterns

### Type Generation

```json
// package.json
{
  "scripts": {
    "generate:types": "payload generate:types",
    "generate:graphql": "payload generate:graphql-schema"
  }
}
```

### Using Generated Types

```typescript
import type { Post, User } from '@/payload-types'

// Type-safe queries
const posts: Post[] = await payload.find({
  collection: 'posts',
  where: {
    author: {
      equals: user.id,
    },
  },
}).then(res => res.docs)
```

## Performance Optimization

### Indexing Strategy

```typescript
fields: [
  {
    name: 'slug',
    type: 'text',
    unique: true,
    index: true, // Create database index
  },
  {
    name: 'status',
    type: 'select',
    index: true, // Frequently queried field
    options: ['draft', 'published'],
  },
]

// Compound indexes
indexes: [
  {
    fields: {
      status: 1,
      publishedAt: -1,
    },
  },
]
```

### Query Optimization

```typescript
// Limit depth to avoid over-fetching
const result = await payload.find({
  collection: 'posts',
  depth: 1, // Only populate one level deep
  select: {
    title: true,
    slug: true,
    publishedAt: true,
  }, // Only select needed fields
})

// Use pagination for large datasets
const paginatedResults = await payload.find({
  collection: 'posts',
  limit: 20,
  page: 1,
  pagination: true,
})
```

## Admin Panel Configuration

```typescript
admin: {
  user: 'users',
  dateFormat: 'yyyy-MM-dd',
  avatar: 'gravatar',
  components: {
    providers: [ThemeProvider],
    Nav: '/components/Nav',
    graphics: {
      Logo: '/components/Logo',
      Icon: '/components/Icon',
    },
  },
  meta: {
    titleSuffix: '- Payload CMS',
    favicon: '/favicon.ico',
    ogImage: '/og-image.jpg',
  },
  livePreview: {
    url: ({ data, documentInfo }) => 
      `${process.env.NEXT_PUBLIC_SITE_URL}/preview/${documentInfo.slug}`,
    collections: ['pages', 'posts'],
  },
}
```

## Custom Endpoints

```typescript
endpoints: [
  {
    path: '/search',
    method: 'get',
    handler: async (req, res) => {
      const { q } = req.query
      
      const results = await req.payload.find({
        collection: 'posts',
        where: {
          or: [
            { title: { like: q } },
            { content: { like: q } },
          ],
        },
      })
      
      res.json(results)
    },
  },
]
```

## Error Handling

```typescript
try {
  const result = await payload.create({
    collection: 'posts',
    data: { /* ... */ },
  })
  return result
} catch (error) {
  if (error instanceof ValidationError) {
    // Handle validation errors
    console.error('Validation failed:', error.data)
    return { error: 'Validation failed', details: error.data }
  }
  
  if (error instanceof Forbidden) {
    // Handle access control errors
    return { error: 'Access denied' }
  }
  
  if (error instanceof APIError) {
    // Handle general API errors
    return { error: error.message }
  }
  
  // Unexpected errors
  throw error
}
```

## AI Reasoning for Payload Development

When developing with PayloadCMS, consider:

- **Schema Design**: Design collections with relationships in mind. Use virtual fields for computed values. Consider using blocks for flexible content structures.

- **Access Control**: Implement granular access control at collection and field levels. Use query constraints for row-level security.

- **Performance**: Index frequently queried fields. Limit population depth. Use field selection to reduce payload size.

- **Validation**: Combine field-level and collection-level validation. Use async validation sparingly due to performance impact.

- **Hooks**: Choose the appropriate hook for your use case. Use field hooks for data transformation, collection hooks for business logic.

- **Type Safety**: Generate and use TypeScript types. Avoid type assertions unless absolutely necessary.

- **Admin UI**: Customize the admin panel with conditional logic, custom components, and field descriptions to improve editor experience.

- **Localization**: Plan for localization early if needed. Use localized fields and configure locales in base config.

- **Media Handling**: Configure appropriate image sizes. Use focal point for better cropping. Consider CDN integration for production.

- **API Design**: Leverage auto-generated REST and GraphQL APIs. Add custom endpoints only when necessary.